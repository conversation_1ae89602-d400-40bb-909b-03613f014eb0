package com.example.aimusicplayer.di;

import android.content.Context;
import com.example.aimusicplayer.data.db.dao.PlayHistoryDao;
import com.example.aimusicplayer.data.db.dao.PlaylistDao;
import com.example.aimusicplayer.data.db.dao.SongDao;
import com.example.aimusicplayer.data.source.MusicDataSource;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;
import okhttp3.OkHttpClient;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata("dagger.hilt.android.qualifiers.ApplicationContext")
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class AppModule_ProvideMusicDataSourceFactory implements Factory<MusicDataSource> {
  private final Provider<Context> contextProvider;

  private final Provider<OkHttpClient> okHttpClientProvider;

  private final Provider<SongDao> songDaoProvider;

  private final Provider<PlaylistDao> playlistDaoProvider;

  private final Provider<PlayHistoryDao> playHistoryDaoProvider;

  public AppModule_ProvideMusicDataSourceFactory(Provider<Context> contextProvider,
      Provider<OkHttpClient> okHttpClientProvider, Provider<SongDao> songDaoProvider,
      Provider<PlaylistDao> playlistDaoProvider, Provider<PlayHistoryDao> playHistoryDaoProvider) {
    this.contextProvider = contextProvider;
    this.okHttpClientProvider = okHttpClientProvider;
    this.songDaoProvider = songDaoProvider;
    this.playlistDaoProvider = playlistDaoProvider;
    this.playHistoryDaoProvider = playHistoryDaoProvider;
  }

  @Override
  public MusicDataSource get() {
    return provideMusicDataSource(contextProvider.get(), okHttpClientProvider.get(), songDaoProvider.get(), playlistDaoProvider.get(), playHistoryDaoProvider.get());
  }

  public static AppModule_ProvideMusicDataSourceFactory create(Provider<Context> contextProvider,
      Provider<OkHttpClient> okHttpClientProvider, Provider<SongDao> songDaoProvider,
      Provider<PlaylistDao> playlistDaoProvider, Provider<PlayHistoryDao> playHistoryDaoProvider) {
    return new AppModule_ProvideMusicDataSourceFactory(contextProvider, okHttpClientProvider, songDaoProvider, playlistDaoProvider, playHistoryDaoProvider);
  }

  public static MusicDataSource provideMusicDataSource(Context context, OkHttpClient okHttpClient,
      SongDao songDao, PlaylistDao playlistDao, PlayHistoryDao playHistoryDao) {
    return Preconditions.checkNotNullFromProvides(AppModule.INSTANCE.provideMusicDataSource(context, okHttpClient, songDao, playlistDao, playHistoryDao));
  }
}
