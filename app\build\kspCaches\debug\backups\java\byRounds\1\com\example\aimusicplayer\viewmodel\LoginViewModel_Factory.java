package com.example.aimusicplayer.viewmodel;

import android.app.Application;
import com.example.aimusicplayer.data.repository.UserRepository;
import com.example.aimusicplayer.error.GlobalErrorHandler;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class LoginViewModel_Factory implements Factory<LoginViewModel> {
  private final Provider<Application> applicationProvider;

  private final Provider<UserRepository> userRepositoryProvider;

  private final Provider<GlobalErrorHandler> errorHandlerProvider;

  public LoginViewModel_Factory(Provider<Application> applicationProvider,
      Provider<UserRepository> userRepositoryProvider,
      Provider<GlobalErrorHandler> errorHandlerProvider) {
    this.applicationProvider = applicationProvider;
    this.userRepositoryProvider = userRepositoryProvider;
    this.errorHandlerProvider = errorHandlerProvider;
  }

  @Override
  public LoginViewModel get() {
    return newInstance(applicationProvider.get(), userRepositoryProvider.get(), errorHandlerProvider.get());
  }

  public static LoginViewModel_Factory create(Provider<Application> applicationProvider,
      Provider<UserRepository> userRepositoryProvider,
      Provider<GlobalErrorHandler> errorHandlerProvider) {
    return new LoginViewModel_Factory(applicationProvider, userRepositoryProvider, errorHandlerProvider);
  }

  public static LoginViewModel newInstance(Application application, UserRepository userRepository,
      GlobalErrorHandler errorHandler) {
    return new LoginViewModel(application, userRepository, errorHandler);
  }
}
