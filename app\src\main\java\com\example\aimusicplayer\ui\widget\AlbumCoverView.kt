package com.example.aimusicplayer.ui.widget

import android.animation.Animator
import android.animation.AnimatorListenerAdapter
import android.animation.ValueAnimator
import android.animation.ValueAnimator.AnimatorUpdateListener
import android.content.Context
import android.graphics.Bitmap
import android.graphics.BitmapFactory
import android.graphics.Canvas
import android.graphics.Matrix
import android.graphics.Paint
import android.graphics.Point
import android.graphics.PorterDuff
import android.graphics.PorterDuffXfermode
import android.graphics.RectF
import android.graphics.drawable.Drawable
import android.util.AttributeSet
import android.view.View
import android.view.animation.AccelerateInterpolator
import android.view.animation.DecelerateInterpolator
import android.view.animation.LinearInterpolator
import androidx.core.content.res.ResourcesCompat
import com.example.aimusicplayer.R
import com.example.aimusicplayer.utils.ImageUtils

/**
 * 专辑封面视图
 * 实现黑胶唱片旋转动画效果
 */
class AlbumCoverView @JvmOverloads constructor(
    context: Context?,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : View(context, attrs, defStyleAttr) {

    // 黑胶唱片背景
    private var discBitmap = BitmapFactory.decodeResource(resources, R.drawable.bg_playing_disc)
    private val discMatrix by lazy { Matrix() }
    private val discStartPoint by lazy { Point() } // 图片起始坐标
    private val discCenterPoint by lazy { Point() } // 旋转中心坐标
    private var discRotation = 0.0f

    // 唱针
    private var needleBitmap = BitmapFactory.decodeResource(resources, R.drawable.ic_playing_needle)
    private val needleMatrix by lazy { Matrix() }
    private val needleStartPoint by lazy { Point() }
    private val needleCenterPoint by lazy { Point() }
    private var needleRotation = NEEDLE_ROTATION_PLAY

    // 专辑封面
    private var coverBitmap: Bitmap? = null
    private val coverMatrix by lazy { Matrix() }
    private val coverStartPoint by lazy { Point() }
    private val coverCenterPoint by lazy { Point() }
    private var coverSize = 0

    // 绘制相关
    private val paint = Paint(Paint.ANTI_ALIAS_FLAG)
    private val circlePaint = Paint(Paint.ANTI_ALIAS_FLAG)
    private val coverRect = RectF()

    // 动画相关
    private val coverBorder: Drawable by lazy {
        ResourcesCompat.getDrawable(resources, R.drawable.bg_playing_cover_border, null)!!
    }

    private val rotationAnimator by lazy {
        ValueAnimator.ofFloat(0f, 360f).apply {
            duration = 20000
            repeatCount = ValueAnimator.INFINITE
            interpolator = LinearInterpolator()
            addUpdateListener(rotationUpdateListener)
            // 优化动画性能
            setFloatValues(0f, 360f)
        }
    }
    private val playAnimator by lazy {
        ValueAnimator.ofFloat(NEEDLE_ROTATION_PAUSE, NEEDLE_ROTATION_PLAY).apply {
            duration = 300
            addUpdateListener(animationUpdateListener)
        }
    }
    private val pauseAnimator by lazy {
        ValueAnimator.ofFloat(NEEDLE_ROTATION_PLAY, NEEDLE_ROTATION_PAUSE).apply {
            duration = 300
            addUpdateListener(animationUpdateListener)
        }
    }

    // 状态
    private var isPlaying = false
    private var lastRotation = 0f // 记录上次旋转角度，用于恢复旋转

    init {
        // 初始化画笔
        circlePaint.color = 0xFF111111.toInt() // 黑色
        circlePaint.style = Paint.Style.FILL

        // 设置混合模式，用于绘制圆形封面
        paint.xfermode = PorterDuffXfermode(PorterDuff.Mode.SRC_IN)
    }

    override fun onSizeChanged(w: Int, h: Int, oldw: Int, oldh: Int) {
        super.onSizeChanged(w, h, oldw, oldh)
        if (w > 0 && h > 0) {
            initSize()
        }
    }

    private fun initSize() {
        val unit = width.coerceAtMost(height) / 8

        needleBitmap = ImageUtils.resizeImage(needleBitmap, unit * 2, (unit * 3.33).toInt())
        needleStartPoint.x = (width / 2 - needleBitmap.width / 5.5f).toInt()
        needleStartPoint.y = 0
        needleCenterPoint.x = width / 2
        needleCenterPoint.y = (needleBitmap.width / 5.5f).toInt()

        discBitmap = ImageUtils.resizeImage(discBitmap, unit * 6, unit * 6)
        val discOffsetY = (needleBitmap.height / 1.5).toInt()
        discStartPoint.x = (width - discBitmap.width) / 2
        discStartPoint.y = discOffsetY
        discCenterPoint.x = width / 2
        discCenterPoint.y = discBitmap.height / 2 + discOffsetY

        coverSize = unit * 4
        coverStartPoint.x = (width - coverSize) / 2
        coverStartPoint.y = discOffsetY + (discBitmap.height - coverSize) / 2
        coverCenterPoint.x = discCenterPoint.x
        coverCenterPoint.y = discCenterPoint.y
    }

    override fun onDraw(canvas: Canvas) {
        // 保存画布状态
        val saveCount = canvas.saveLayer(0f, 0f, width.toFloat(), height.toFloat(), null)

        // 1. 绘制黑胶唱片
        discMatrix.reset()
        discMatrix.setRotate(
            discRotation,
            discCenterPoint.x.toFloat(),
            discCenterPoint.y.toFloat()
        )
        discMatrix.preTranslate(discStartPoint.x.toFloat(), discStartPoint.y.toFloat())
        canvas.drawBitmap(discBitmap, discMatrix, null)

        // 2. 绘制圆形封面
        val cover = coverBitmap
        if (cover != null) {
            // 先绘制一个圆形
            canvas.drawCircle(
                coverCenterPoint.x.toFloat(),
                coverCenterPoint.y.toFloat(),
                coverSize / 2f,
                circlePaint
            )

            // 使用SRC_IN模式绘制封面，这样封面就会被裁剪成圆形
            coverMatrix.reset()
            coverMatrix.setRotate(
                discRotation, // 封面跟随唱片旋转
                coverCenterPoint.x.toFloat(),
                coverCenterPoint.y.toFloat()
            )
            coverMatrix.preTranslate(coverStartPoint.x.toFloat(), coverStartPoint.y.toFloat())
            coverMatrix.preScale(
                coverSize.toFloat() / cover.width,
                coverSize.toFloat() / cover.height
            )

            // 使用混合模式绘制封面
            canvas.drawBitmap(cover, coverMatrix, paint)
        }

        // 恢复画布状态
        canvas.restoreToCount(saveCount)

        // 3. 绘制唱针（可选，如果不需要唱针可以注释掉）
        needleMatrix.reset()
        needleMatrix.setRotate(
            needleRotation,
            needleCenterPoint.x.toFloat(),
            needleCenterPoint.y.toFloat()
        )
        needleMatrix.preTranslate(needleStartPoint.x.toFloat(), needleStartPoint.y.toFloat())
        canvas.drawBitmap(needleBitmap, needleMatrix, null)
    }

    fun initNeedle(isPlaying: Boolean) {
        needleRotation = if (isPlaying) NEEDLE_ROTATION_PLAY else NEEDLE_ROTATION_PAUSE
        invalidate()
    }

    /**
     * 设置专辑封面
     * @param bitmap 封面图片
     */
    fun setCoverBitmap(bitmap: Bitmap) {
        coverBitmap = bitmap
        invalidate()
    }

    /**
     * 开始播放动画
     */
    fun start() {
        if (isPlaying) {
            return
        }
        isPlaying = true
        rotationAnimator.start()
        playAnimator.start()
    }

    /**
     * 暂停播放动画
     */
    fun pause() {
        if (!isPlaying) {
            return
        }
        isPlaying = false
        rotationAnimator.pause()
        pauseAnimator.start()
    }

    /**
     * 重置动画状态
     */
    fun reset() {
        isPlaying = false
        discRotation = 0.0f
        rotationAnimator.cancel()
        invalidate()
    }

    /**
     * 唱片旋转动画更新监听器
     */
    private val rotationUpdateListener = AnimatorUpdateListener { animation ->
        discRotation = animation.animatedValue as Float
        invalidate()
    }

    private val animationUpdateListener = AnimatorUpdateListener { animation ->
        needleRotation = animation.animatedValue as Float
        invalidate()
    }

    /**
     * 设置硬件加速
     * 提高动画流畅度
     */
    override fun onAttachedToWindow() {
        super.onAttachedToWindow()
        // 启用硬件加速
        setLayerType(LAYER_TYPE_HARDWARE, null)
    }

    /**
     * 释放资源
     */
    override fun onDetachedFromWindow() {
        super.onDetachedFromWindow()
        // 取消所有动画
        rotationAnimator.cancel()
        playAnimator.cancel()
        pauseAnimator.cancel()
    }

    companion object {
        private const val NEEDLE_ROTATION_PLAY = 0.0f
        private const val NEEDLE_ROTATION_PAUSE = -25.0f

        private const val COVER_BORDER_WIDTH = 6
    }
}
