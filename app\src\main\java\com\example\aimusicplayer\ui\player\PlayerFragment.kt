package com.example.aimusicplayer.ui.player

import android.animation.ObjectAnimator
import android.animation.ValueAnimator
import android.app.AlertDialog
import android.content.Context
import android.content.Intent
import android.os.Build
import android.os.VibrationEffect
import android.os.Vibrator
import android.graphics.Bitmap
import android.graphics.BitmapFactory
import android.graphics.Color
import android.graphics.drawable.ColorDrawable
import android.graphics.drawable.Drawable // 导入 Drawable
// import androidx.graphics.drawable.Drawable // Removed duplicate import
import android.os.Bundle
import android.util.Log
import android.view.LayoutInflater
import android.view.MotionEvent
import android.view.View
import android.view.ViewGroup
import android.widget.Button
import android.widget.EditText
import android.widget.ImageButton
import android.widget.ImageView
import android.widget.SeekBar
import android.widget.TextView
import android.widget.Toast
import androidx.core.content.ContextCompat
import androidx.core.graphics.ColorUtils
import androidx.fragment.app.Fragment
import android.view.animation.LinearInterpolator
import androidx.fragment.app.viewModels
import androidx.lifecycle.lifecycleScope
import androidx.navigation.fragment.findNavController
import androidx.palette.graphics.Palette
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import androidx.swiperefreshlayout.widget.SwipeRefreshLayout
import com.bumptech.glide.Glide
import com.bumptech.glide.load.DataSource
import com.bumptech.glide.load.engine.DiskCacheStrategy
import com.bumptech.glide.load.engine.GlideException
import com.bumptech.glide.request.RequestListener
import com.bumptech.glide.request.RequestOptions
import com.bumptech.glide.request.target.CustomTarget
import com.bumptech.glide.request.target.Target
import com.bumptech.glide.request.transition.Transition
import com.example.aimusicplayer.R
import com.example.aimusicplayer.ui.adapter.CommentAdapter
import com.example.aimusicplayer.adapter.MediaItemAdapter
import com.example.aimusicplayer.databinding.FragmentPlayerBinding
import com.example.aimusicplayer.ui.player.LyricAdapter
import com.example.aimusicplayer.ui.player.EmptyLyricAdapter
import com.example.aimusicplayer.service.PlayMode
import com.example.aimusicplayer.service.PlayState
import com.example.aimusicplayer.utils.AlbumArtCache
import com.example.aimusicplayer.utils.EnhancedImageCache
import com.example.aimusicplayer.utils.ImageUtils
import com.example.aimusicplayer.utils.TimeUtils
import com.example.aimusicplayer.data.model.LyricInfo
import com.example.aimusicplayer.data.model.LyricLine
import com.example.aimusicplayer.viewmodel.PlayerViewModel
import com.google.android.material.bottomsheet.BottomSheetDialog
import com.google.android.material.snackbar.Snackbar
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import javax.inject.Inject

/**
 * 播放器Fragment
 * 使用MVVM架构
 */
@AndroidEntryPoint
class PlayerFragment : Fragment() {

    private var _binding: FragmentPlayerBinding? = null
    private val binding get() = _binding!!

    private val viewModel: PlayerViewModel by viewModels()

    @Inject
    lateinit var albumArtCache: AlbumArtCache

    // 增强型图片缓存
    private lateinit var enhancedImageCache: EnhancedImageCache

    // 状态变量
    private var isSeekBarDragging = false
    private var isLyricMode = false
    private var backgroundColorAnimator: android.animation.ValueAnimator? = null

    // 专辑旋转动画
    private var albumRotationAnimator: ObjectAnimator? = null
    private var currentRotation = 0f

    // 歌曲切换动画
    private var songTransitionAnimator: ValueAnimator? = null
    private var previousSongId: Long = -1

    // 歌词拖动相关变量
    private var lyricDragging = false
    private var lyricDragStartY = 0f
    private var lyricDragStartTime = 0L

    // 动画相关常量
    companion object {
        private const val BACKGROUND_ANIMATION_DURATION = 800L
        private const val COVER_ANIMATION_DURATION = 500L
        private const val SONG_TRANSITION_DURATION = 600L
        private const val ALBUM_ROTATION_DURATION = 20000L // 20秒旋转一圈
        private const val TAG = "PlayerFragment"
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = FragmentPlayerBinding.inflate(inflater, container, false)

        // 初始化增强型图片缓存
        enhancedImageCache = EnhancedImageCache.getInstance(requireContext())

        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        // 初始化UI
        initializeUI()

        // 设置观察者
        setupObservers()
    }

    /**
     * 初始化UI
     */
    private fun initializeUI() {
        // 设置播放/暂停按钮点击事件
        binding.buttonPlayerPlayPause.setOnClickListener {
            addButtonClickEffect(it)
            viewModel.togglePlayPause()
        }

        // 设置上一首按钮点击事件
        binding.buttonPlayerPrev.setOnClickListener {
            addButtonClickEffect(it)
            viewModel.skipToPrevious()
        }

        // 设置下一首按钮点击事件
        binding.buttonPlayerNext.setOnClickListener {
            addButtonClickEffect(it)
            viewModel.skipToNext()
        }

        // 设置播放模式按钮点击事件
        binding.buttonPlayerPlayMode.setOnClickListener {
            addButtonClickEffect(it)
            viewModel.togglePlayMode()
        }

        // 设置收藏按钮点击事件
        binding.buttonPlayerCollect.setOnClickListener {
            addButtonClickEffect(it)
            toggleCollect()
        }

        // 设置播放列表按钮点击事件
        binding.buttonPlayerPlaylist.setOnClickListener {
            addButtonClickEffect(it)
            showPlaylistDialog()
        }

        // 设置心动模式按钮点击事件
        binding.buttonPlayerIntelligence.setOnClickListener {
            addButtonClickEffect(it)
            showHeartModeDialog()
        }

        // 设置评论按钮点击事件
        binding.buttonPlayerComment.setOnClickListener {
            addButtonClickEffect(it)
            showCommentDialog()
        }

        // 设置分享按钮点击事件
        binding.buttonPlayerShare.setOnClickListener {
            addButtonClickEffect(it)
            shareSong()
        }

        // 专辑封面和歌词切换
        binding.albumArt.setOnClickListener {
            toggleLyricMode()
        }

        binding.viewPagerPlayer.setOnClickListener {
            toggleLyricMode()
        }

        // 进度条
        binding.seekbarPlayerProgress.setOnSeekBarChangeListener(object : SeekBar.OnSeekBarChangeListener {
            override fun onProgressChanged(seekBar: SeekBar?, progress: Int, fromUser: Boolean) {
                if (fromUser) {
                    binding.textviewPlayerCurrentTime.text = TimeUtils.formatTime(progress.toLong())
                }
            }

            override fun onStartTrackingTouch(seekBar: SeekBar?) {
                isSeekBarDragging = true
            }

            override fun onStopTrackingTouch(seekBar: SeekBar?) {
                isSeekBarDragging = false
                seekBar?.progress?.let { progress ->
                    viewModel.seekTo(progress)
                }
            }
        })

        // 设置歌词点击跳转
        setupLyricInteraction()
    }

    /**
     * 添加按钮点击效果
     * 增强版：添加透明度变化、颜色变化和波纹效果
     */
    private fun addButtonClickEffect(view: View) {
        // 保存原始状态
        val originalAlpha = view.alpha
        val originalScaleX = view.scaleX
        val originalScaleY = view.scaleY

        // 获取按钮背景颜色
        val background = view.background

        // 创建按钮点击动画
        view.animate()
            .scaleX(0.85f)
            .scaleY(0.85f)
            .alpha(0.7f)
            .setDuration(80)
            .withEndAction {
                view.animate()
                    .scaleX(originalScaleX)
                    .scaleY(originalScaleY)
                    .alpha(originalAlpha)
                    .setDuration(120)
                    .start()
            }
            .start()

        // 添加波纹效果
        if (view is ImageView) {
            // 创建临时的圆形波纹视图
            val rippleView = View(requireContext())
            rippleView.layoutParams = ViewGroup.LayoutParams(
                view.width,
                view.height
            )
            rippleView.background = ContextCompat.getDrawable(requireContext(), R.drawable.ripple_oval_button)

            // 添加到父视图
            val parent = view.parent as ViewGroup
            val index = parent.indexOfChild(view)
            parent.addView(rippleView, index)

            // 设置位置
            rippleView.x = view.x
            rippleView.y = view.y

            // 触发波纹效果
            rippleView.isPressed = true
            rippleView.isPressed = false

            // 延迟移除波纹视图
            rippleView.postDelayed({
                parent.removeView(rippleView)
            }, 300)
        }

        // 添加触觉反馈
        if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.O) {
            val vibrator = requireActivity().getSystemService(android.content.Context.VIBRATOR_SERVICE) as android.os.Vibrator
            vibrator.vibrate(android.os.VibrationEffect.createOneShot(20, android.os.VibrationEffect.DEFAULT_AMPLITUDE))
        }
    }

    /**
     * 设置观察者
     */
    private fun setupObservers() {
        // 观察当前歌曲
        viewModel.currentSong.observe(viewLifecycleOwner) { mediaItem ->
            mediaItem?.let {
                // 获取歌曲ID
                val songId = it.mediaId.toLongOrNull() ?: 0L

                // 检查是否是新歌曲
                val isSongChanged = previousSongId != songId

                // 如果是新歌曲，添加切换动画
                if (isSongChanged && previousSongId != -1L) {
                    playSongTransitionAnimation()
                }

                // 更新歌曲信息
                binding.songTitle.text = it.mediaMetadata.title
                binding.songArtist.text = it.mediaMetadata.artist

                // 加载专辑封面
                val artworkUri = it.mediaMetadata.artworkUri

                if (artworkUri != null) {
                    // 使用增强型缓存机制加载专辑封面
                    lifecycleScope.launch {
                        try {
                            // 生成缓存键
                            val cacheKey = "album_${songId}"

                            // 从缓存获取
                            val bitmap = enhancedImageCache.getBitmap(artworkUri, cacheKey)

                            if (bitmap != null) {
                                // 使用缓存的专辑封面
                                updateAlbumArt(bitmap, isSongChanged)
                            } else {
                                // 如果缓存中没有，使用默认封面
                                try {
                                    val defaultCover = BitmapFactory.decodeResource(resources, R.drawable.default_album_art)
                                    if (defaultCover != null) {
                                        updateAlbumArt(defaultCover, isSongChanged)
                                    }
                                } catch (e: Exception) {
                                    Log.e(TAG, "加载默认封面失败", e)
                                }

                                // 异步加载并缓存
                                loadAlbumArtWithEnhancedCache(artworkUri, songId)
                            }
                        } catch (e: Exception) {
                            Log.e(TAG, "加载专辑封面失败", e)
                            // 加载失败，使用默认封面
                            try {
                                val defaultCover = BitmapFactory.decodeResource(resources, R.drawable.default_album_art)
                                if (defaultCover != null) {
                                    updateAlbumArt(defaultCover, isSongChanged)
                                }
                            } catch (ex: Exception) {
                                Log.e(TAG, "加载默认封面失败", ex)
                            }
                        }
                    }
                } else {
                    // 使用默认封面
                    try {
                        val defaultCover = BitmapFactory.decodeResource(resources, R.drawable.default_album_art)
                        if (defaultCover != null) {
                            updateAlbumArt(defaultCover, isSongChanged)
                        }
                    } catch (e: Exception) {
                        Log.e(TAG, "加载默认封面失败", e)
                    }
                }

                // 加载歌词
                viewModel.loadLyricInfo(songId)

                // 更新收藏状态
                updateCollectButton(viewModel.isCurrentSongCollected.value ?: false)

                // 保存当前歌曲ID
                previousSongId = songId
            }
        }

        // 观察播放状态
        viewModel.playState.observe(viewLifecycleOwner) { state ->
            when (state) {
                is PlayState.Playing -> {
                    binding.buttonPlayerPlayPause.isSelected = true
                    startAlbumRotation()
                }
                is PlayState.Pause -> {
                    binding.buttonPlayerPlayPause.isSelected = false
                    pauseAlbumRotation()
                }
                is PlayState.Idle -> {
                    binding.buttonPlayerPlayPause.isSelected = false
                    resetAlbumRotation()
                }
                else -> {
                    // 其他状态
                }
            }
        }

        // 观察播放进度
        viewModel.playProgress.observe(viewLifecycleOwner) { currentPositionMs ->
            if (!isSeekBarDragging) {
                binding.seekbarPlayerProgress.progress = currentPositionMs.toInt()
                binding.textviewPlayerCurrentTime.text = TimeUtils.formatTime(currentPositionMs)
                val totalDuration = viewModel.duration.value ?: 0L
                binding.textviewPlayerTotalTime.text = TimeUtils.formatTime(totalDuration)
                binding.seekbarPlayerProgress.max = totalDuration.toInt()

                // 更新歌词位置
                updateLyricPosition(currentPositionMs)
            }
        }

        // 观察播放模式
        viewModel.playMode.observe(viewLifecycleOwner) { mode ->
            updatePlayModeButton(mode)
        }

        // 观察歌词
        viewModel.lyrics.observe(viewLifecycleOwner) { lyricInfo ->
            if (lyricInfo != null && !lyricInfo.isEmpty()) {
                // 设置歌词适配器
                val lyricAdapter = LyricAdapter(requireContext(), lyricInfo)
                binding.viewPagerPlayer.adapter = lyricAdapter

                // 更新当前播放位置对应的歌词
                updateLyricPosition(viewModel.currentPosition.value ?: 0)
            } else {
                // 没有歌词时显示提示
                val emptyLyricAdapter = EmptyLyricAdapter(requireContext())
                binding.viewPagerPlayer.adapter = emptyLyricAdapter
            }
        }

        // 观察当前歌曲是否已收藏
        viewModel.isCurrentSongCollected.observe(viewLifecycleOwner) { isCollected ->
            updateCollectButton(isCollected)
        }

        // 观察错误弹窗
        viewModel.showErrorDialog.observe(viewLifecycleOwner) { errorMessage ->
            if (!errorMessage.isNullOrEmpty()) {
                showErrorDialog("错误", errorMessage)
            }
        }
    }

    /**
     * 更新专辑封面 - 优化版本，增强错误处理
     * @param bitmap 专辑封面位图
     * @param withAnimation 是否添加动画效果
     */
    private fun updateAlbumArt(bitmap: Bitmap?, withAnimation: Boolean = false) {
        try {
            // 检查bitmap是否有效
            if (bitmap == null || bitmap.isRecycled) {
                Log.w(TAG, "位图无效，使用默认封面")
                val defaultBitmap = BitmapFactory.decodeResource(resources, R.drawable.default_album_art)
                if (defaultBitmap != null) {
                    updateAlbumArt(defaultBitmap, withAnimation)
                }
                return
            }

            // 设置到AlbumCoverView
            binding.albumCoverView.setCoverBitmap(bitmap)

            // 同时设置到备用ImageView（如果需要）
            if (withAnimation) {
                // 优化动画，减少重绘
                binding.albumArt.animate()
                    .alpha(0f)
                    .scaleX(0.9f)
                    .scaleY(0.9f)
                    .setDuration(COVER_ANIMATION_DURATION / 2)
                    .withEndAction {
                        try {
                            // 设置新封面
                            binding.albumArt.setImageBitmap(bitmap)

                            // 淡入新封面
                            binding.albumArt.animate()
                                .alpha(1f)
                                .scaleX(1f)
                                .scaleY(1f)
                                .setDuration(COVER_ANIMATION_DURATION / 2)
                                .start()
                        } catch (e: Exception) {
                            Log.e(TAG, "动画设置封面失败", e)
                        }
                    }
                    .start()
            } else {
                // 直接设置专辑封面
                binding.albumArt.setImageBitmap(bitmap)
            }
        } catch (e: Exception) {
            Log.e(TAG, "更新专辑封面失败", e)
            // 显示用户友好的错误提示
            showErrorSnackbar("专辑封面加载失败")
        }

        // 生成调色板并提取颜色
        Palette.from(bitmap).generate { palette ->
            palette?.let {
                // 获取主色调
                val dominantColor = it.getDominantColor(0xFF333333.toInt())

                // 获取鲜艳的颜色
                val vibrantColor = it.getVibrantColor(dominantColor)

                // 使用鲜艳的颜色或主色调
                val finalColor = if (vibrantColor != dominantColor) vibrantColor else dominantColor

                // 更新背景颜色
                updateBackgroundColor(finalColor)

                // 获取文本颜色
                val textColor = if (ColorUtils.calculateLuminance(finalColor) > 0.5) {
                    Color.BLACK
                } else {
                    Color.WHITE
                }

                // 更新文本颜色
                updateTextColor(textColor)
            }
        }

        // 生成模糊背景
        lifecycleScope.launch {
            val blurredBitmap = withContext(Dispatchers.IO) {
                ImageUtils.blurBitmap(requireContext(), bitmap, 25)
            }
            updateBlurredBackground(blurredBitmap, withAnimation)
        }
    }

    /**
     * 使用增强型缓存加载专辑封面
     */
    private fun loadAlbumArtWithEnhancedCache(artworkUri: android.net.Uri, songId: Long) {
        // 生成缓存键
        val cacheKey = "album_${songId}"

        // 加载专辑封面到ImageView
        Glide.with(this)
            .asBitmap()
            .load(artworkUri)
            .apply(RequestOptions()
                .diskCacheStrategy(DiskCacheStrategy.ALL)
                .centerCrop())
            .into(object : CustomTarget<Bitmap>() {
                override fun onResourceReady(resource: Bitmap, transition: Transition<in Bitmap>?) {
                    // 设置专辑封面
                    binding.albumArt.setImageBitmap(resource)

                    // 提取颜色
                    extractColorFromBitmap(resource)

                    // 生成模糊背景
                    lifecycleScope.launch {
                        val blurredBitmap = withContext(Dispatchers.IO) {
                            ImageUtils.blurBitmap(requireContext(), resource, 25)
                        }
                        updateBlurredBackground(blurredBitmap)

                        // 缓存模糊背景
                        enhancedImageCache.saveBitmap(blurredBitmap, "blur_${cacheKey}")
                    }

                    // 缓存专辑封面
                    lifecycleScope.launch(Dispatchers.IO) {
                        enhancedImageCache.saveBitmap(resource, cacheKey)
                    }
                }

                override fun onLoadFailed(errorDrawable: Drawable?) {
                    Log.e(TAG, "加载专辑封面失败")
                }

                override fun onLoadCleared(placeholder: Drawable?) {
                    // 不需要处理
                }
            })
    }

    /**
     * 从位图中提取颜色
     */
    private fun extractColorFromBitmap(bitmap: Bitmap) {
        lifecycleScope.launch(Dispatchers.Default) {
            // 使用Palette提取颜色
            val palette = Palette.from(bitmap).generate()

            // 获取主色调
            val dominantColor = palette.getDominantColor(0xFF333333.toInt())

            // 获取鲜艳的颜色
            val vibrantColor = palette.getVibrantColor(dominantColor)

            // 使用鲜艳的颜色或主色调
            val finalColor = if (vibrantColor != dominantColor) vibrantColor else dominantColor

            // 在主线程更新UI
            withContext(Dispatchers.Main) {
                // 更新背景颜色
                updateBackgroundColor(finalColor)

                // 获取文本颜色
                val textColor = if (ColorUtils.calculateLuminance(finalColor) > 0.5) {
                    Color.BLACK
                } else {
                    Color.WHITE
                }

                // 更新文本颜色
                updateTextColor(textColor)
            }
        }
    }

    /**
     * 设置歌词点击跳转
     */
    /**
     * 设置歌词交互
     */
    private fun setupLyricInteraction() {
        // 设置歌词点击事件
        binding.viewPagerPlayer.setOnClickListener {
            // 获取当前歌词适配器
            val adapter = binding.viewPagerPlayer.adapter
            if (adapter is LyricAdapter) {
                // 获取点击位置
                val y = it.y
                val height = binding.viewPagerPlayer.height

                // 计算点击位置在歌词中的相对位置
                val relativePosition = y / height

                // 根据相对位置计算歌词行索引
                val lineCount = adapter.getItemCount()
                val clickedLine = (relativePosition * lineCount).toInt()

                // 获取该行歌词的时间戳
                val lyricLine = adapter.getLyricLine(clickedLine)
                if (lyricLine != null) {
                    // 跳转到对应时间
                    viewModel.seekTo(lyricLine.time.toInt())
                }
            } else {
                // 如果不是歌词适配器，切换歌词/封面显示
                toggleLyricMode()
            }
        }

        // 设置歌词拖动事件
        binding.viewPagerPlayer.setOnTouchListener { _, event ->
            when (event.action) {
                MotionEvent.ACTION_DOWN -> {
                    // 记录开始拖动的位置
                    lyricDragStartY = event.y
                    lyricDragStartTime = System.currentTimeMillis()
                    lyricDragging = true
                    true
                }
                MotionEvent.ACTION_MOVE -> {
                    if (lyricDragging) {
                        // 计算拖动距离
                        val deltaY = event.y - lyricDragStartY

                        // 获取当前歌词适配器
                        val adapter = binding.viewPagerPlayer.adapter
                        if (adapter is LyricAdapter) {
                            // 根据拖动距离计算时间偏移
                            val timeOffset = (deltaY * 50).toLong() // 每像素50毫秒

                            // 获取当前播放位置
                            val currentPosition = viewModel.currentPosition.value ?: 0

                            // 计算新的播放位置
                            val newPosition = currentPosition - timeOffset

                            // 更新歌词位置
                            updateLyricPosition(newPosition)
                        }
                    }
                    true
                }
                MotionEvent.ACTION_UP, MotionEvent.ACTION_CANCEL -> {
                    if (lyricDragging) {
                        // 计算拖动时间
                        val dragDuration = System.currentTimeMillis() - lyricDragStartTime

                        // 如果拖动时间小于300毫秒，认为是点击事件
                        if (dragDuration < 300 && Math.abs(event.y - lyricDragStartY) < 20) {
                            // 处理点击事件
                            binding.viewPagerPlayer.performClick()
                        } else {
                            // 处理拖动结束事件
                            // 获取当前歌词适配器
                            val adapter = binding.viewPagerPlayer.adapter
                            if (adapter is LyricAdapter) {
                                // 获取当前高亮的歌词行
                                val currentLine = adapter.getCurrentLine()
                                if (currentLine >= 0) {
                                    // 获取该行歌词的时间戳
                                    val lyricLine = adapter.getLyricLine(currentLine)
                                    if (lyricLine != null) {
                                        // 跳转到对应时间
                                        viewModel.seekTo(lyricLine.time.toInt())
                                    }
                                }
                            }
                        }

                        lyricDragging = false
                    }
                    true
                }
                else -> false
            }
        }
    }

    /**
     * 更新歌词位置
     */
    private fun updateLyricPosition(position: Long) {
        // 获取当前歌词适配器
        val adapter = binding.viewPagerPlayer.adapter
        if (adapter is LyricAdapter) {
            // 更新当前播放位置对应的歌词
            adapter.updateCurrentPosition(position)
        }
    }

    /**
     * 更新背景颜色
     */
    private fun updateBackgroundColor(color: Int) {
        // 取消之前的动画
        backgroundColorAnimator?.cancel()

        // 获取当前背景颜色
        val currentColor = (binding.root.background as? ColorDrawable)?.color ?: Color.BLACK

        // 创建颜色过渡动画
        backgroundColorAnimator = android.animation.ValueAnimator.ofArgb(currentColor, color).apply {
            duration = BACKGROUND_ANIMATION_DURATION
            interpolator = android.view.animation.DecelerateInterpolator()
            addUpdateListener { animator ->
                val animatedColor = animator.animatedValue as Int
                binding.root.setBackgroundColor(animatedColor)
            }
            start()
        }
    }

    /**
     * 更新模糊背景
     * @param bitmap 模糊背景位图
     * @param withAnimation 是否添加动画效果
     */
    private fun updateBlurredBackground(bitmap: Bitmap, withAnimation: Boolean = false) {
        if (withAnimation) {
            // 先淡出当前背景
            binding.backgroundBlur.animate()
                .alpha(0f)
                .setDuration(COVER_ANIMATION_DURATION / 2)
                .withEndAction {
                    // 设置新背景
                    binding.backgroundBlur.setImageBitmap(bitmap)

                    // 淡入新背景
                    binding.backgroundBlur.animate()
                        .alpha(0.8f)
                        .setDuration(COVER_ANIMATION_DURATION / 2)
                        .start()
                }
                .start()
        } else {
            // 直接设置模糊背景
            binding.backgroundBlur.setImageBitmap(bitmap)
        }
    }

    /**
     * 播放歌曲切换动画
     */
    private fun playSongTransitionAnimation() {
        // 取消之前的动画
        songTransitionAnimator?.cancel()

        // 创建动画容器
        val container = binding.contentContainer

        // 创建动画
        container.animate()
            .alpha(0.5f)
            .scaleX(0.95f)
            .scaleY(0.95f)
            .setDuration(SONG_TRANSITION_DURATION / 2)
            .withEndAction {
                container.animate()
                    .alpha(1f)
                    .scaleX(1f)
                    .scaleY(1f)
                    .setDuration(SONG_TRANSITION_DURATION / 2)
                    .start()
            }
            .start()

        // 为歌曲标题和艺术家添加动画
        binding.songTitle.animate()
            .alpha(0f)
            .translationYBy(-20f)
            .setDuration(SONG_TRANSITION_DURATION / 2)
            .withEndAction {
                binding.songTitle.translationY = 20f
                binding.songTitle.animate()
                    .alpha(1f)
                    .translationYBy(-20f)
                    .setDuration(SONG_TRANSITION_DURATION / 2)
                    .start()
            }
            .start()

        binding.songArtist.animate()
            .alpha(0f)
            .translationYBy(-15f)
            .setDuration(SONG_TRANSITION_DURATION / 2)
            .withEndAction {
                binding.songArtist.translationY = 15f
                binding.songArtist.animate()
                    .alpha(1f)
                    .translationYBy(-15f)
                    .setDuration(SONG_TRANSITION_DURATION / 2)
                    .start()
            }
            .start()
    }

    /**
     * 开始专辑旋转动画
     */
    private fun startAlbumRotation() {
        // 使用AlbumCoverView的动画
        binding.albumCoverView.start()

        // 取消之前的动画
        albumRotationAnimator?.cancel()

        // 保存当前旋转角度
        currentRotation = binding.vinylBackground.rotation

        // 创建黑胶唱片旋转动画（备用）
        val vinylAnimator = ObjectAnimator.ofFloat(binding.vinylBackground, View.ROTATION, currentRotation, currentRotation + 360f).apply {
            duration = ALBUM_ROTATION_DURATION
            repeatCount = ObjectAnimator.INFINITE
            interpolator = LinearInterpolator()
        }

        // 创建专辑封面旋转动画（备用）
        val albumAnimator = ObjectAnimator.ofFloat(binding.albumArt, View.ROTATION, currentRotation, currentRotation + 360f).apply {
            duration = ALBUM_ROTATION_DURATION
            repeatCount = ObjectAnimator.INFINITE
            interpolator = LinearInterpolator()
        }

        // 保存动画引用
        albumRotationAnimator = vinylAnimator
    }

    /**
     * 暂停专辑旋转动画
     */
    private fun pauseAlbumRotation() {
        // 使用AlbumCoverView的暂停方法
        binding.albumCoverView.pause()

        // 保存当前旋转角度
        currentRotation = binding.vinylBackground.rotation

        // 暂停动画
        albumRotationAnimator?.pause()
    }

    /**
     * 重置专辑旋转动画
     */
    private fun resetAlbumRotation() {
        // 使用AlbumCoverView的重置方法
        binding.albumCoverView.reset()

        // 取消之前的动画
        albumRotationAnimator?.cancel()

        // 重置旋转角度
        binding.vinylBackground.rotation = 0f
        binding.albumArt.rotation = 0f
        currentRotation = 0f
    }

    /**
     * 切换歌词/封面显示模式
     */
    private fun toggleLyricMode() {
        isLyricMode = !isLyricMode

        if (isLyricMode) {
            // 切换到歌词模式
            binding.albumArt.animate()
                .alpha(0f)
                .setDuration(300)
                .withEndAction {
                    binding.albumArt.visibility = View.GONE
                    binding.vinylBackground.visibility = View.GONE
                    binding.viewPagerPlayer.visibility = View.VISIBLE
                    binding.viewPagerPlayer.alpha = 0f
                    binding.viewPagerPlayer.animate()
                        .alpha(1f)
                        .setDuration(300)
                        .start()
                }
                .start()
        } else {
            // 切换到封面模式
            binding.viewPagerPlayer.animate()
                .alpha(0f)
                .setDuration(300)
                .withEndAction {
                    binding.viewPagerPlayer.visibility = View.GONE
                    binding.albumArt.visibility = View.VISIBLE
                    binding.vinylBackground.visibility = View.VISIBLE
                    binding.albumArt.alpha = 0f
                    binding.albumArt.animate()
                        .alpha(1f)
                        .setDuration(300)
                        .start()
                }
                .start()
        }
    }

    /**
     * 更新文本颜色
     */
    private fun updateTextColor(color: Int) {
        // 更新文本颜色
        binding.songTitle.setTextColor(color)
        binding.songArtist.setTextColor(ColorUtils.setAlphaComponent(color, 200))
        binding.textviewPlayerCurrentTime.setTextColor(color)
        binding.textviewPlayerTotalTime.setTextColor(color)
    }

    /**
     * 更新收藏按钮状态
     */
    private fun updateCollectButton(isCollected: Boolean) {
        binding.buttonPlayerCollect.isSelected = isCollected
    }

    /**
     * 切换收藏状态
     */
    private fun toggleCollect() {
        viewModel.toggleCollect()
    }





    /**
     * 分享歌曲
     */
    private fun shareSong() {
        // 检查当前是否有歌曲在播放
        val currentSong = viewModel.currentSong.value ?: run {
            Toast.makeText(requireContext(), "请先播放一首歌曲", Toast.LENGTH_SHORT).show()
            return
        }

        // 创建分享内容
        val shareText = "我正在听 ${currentSong.mediaMetadata.artist} 的 ${currentSong.mediaMetadata.title}，快来听听吧！"

        // 创建分享Intent
        val shareIntent = Intent(Intent.ACTION_SEND).apply {
            type = "text/plain"
            putExtra(Intent.EXTRA_TEXT, shareText)
        }

        // 启动分享
        startActivity(Intent.createChooser(shareIntent, "分享到"))
    }

    /**
     * 更新播放模式按钮
     */
    private fun updatePlayModeButton(mode: PlayMode) {
        when (mode) {
            PlayMode.Loop -> {
                binding.buttonPlayerPlayMode.setImageResource(R.drawable.ic_repeat)
                binding.buttonPlayerPlayMode.contentDescription = getString(R.string.play_mode_loop)
            }
            PlayMode.Single -> {
                binding.buttonPlayerPlayMode.setImageResource(R.drawable.ic_repeat_one)
                binding.buttonPlayerPlayMode.contentDescription = getString(R.string.play_mode_single)
            }
            PlayMode.Shuffle -> {
                binding.buttonPlayerPlayMode.setImageResource(R.drawable.ic_shuffle)
                binding.buttonPlayerPlayMode.contentDescription = getString(R.string.play_mode_shuffle)
            }
        }
    }



    /**
     * 显示播放列表对话框
     * 增强版：添加动画效果
     */
    private fun showPlaylistDialog() {
        // 创建底部对话框
        val dialog = BottomSheetDialog(requireContext(), R.style.BottomSheetDialogTheme)
        val dialogView = layoutInflater.inflate(R.layout.dialog_playlist, null)
        dialog.setContentView(dialogView)

        // 设置动画效果
        setupDialogAnimation(dialog)

        // 获取RecyclerView
        val recyclerView = dialogView.findViewById<RecyclerView>(R.id.recycler_view_playlist)
        recyclerView.layoutManager = LinearLayoutManager(requireContext())

        // 获取播放列表数量文本
        val countTextView = dialogView.findViewById<TextView>(R.id.text_playlist_count)

        // 获取空播放列表提示
        val emptyView = dialogView.findViewById<TextView>(R.id.text_empty_playlist)

        // 创建适配器
        val adapter = MediaItemAdapter(emptyList()) { position ->
            // 点击播放列表项
            viewModel.playAtIndex(position)
            dialog.dismiss()
        }
        recyclerView.adapter = adapter

        // 观察播放列表
        viewModel.playQueue.observe(viewLifecycleOwner) { mediaItems ->
            if (mediaItems.isNotEmpty()) {
                // 更新播放列表数量
                countTextView.text = "(${mediaItems.size}首)"

                // 更新适配器数据
                adapter.updateMediaItems(mediaItems)

                // 显示RecyclerView，隐藏空提示
                recyclerView.visibility = View.VISIBLE
                emptyView.visibility = View.GONE
            } else {
                // 显示空提示，隐藏RecyclerView
                recyclerView.visibility = View.GONE
                emptyView.visibility = View.VISIBLE
            }
        }

        // 获取关闭按钮
        val closeButton = dialogView.findViewById<ImageButton>(R.id.button_playlist_close)

        // 设置关闭按钮点击事件
        closeButton.setOnClickListener {
            dialog.dismiss()
        }

        // 显示对话框
        dialog.show()
    }

    /**
     * 显示评论页面
     * 使用Navigation导航到CommentFragment
     */
    private fun showCommentDialog() {
        // 检查当前是否有歌曲在播放
        if (viewModel.currentSong.value == null) {
            Toast.makeText(requireContext(), "请先播放一首歌曲", Toast.LENGTH_SHORT).show()
            return
        }

        // 获取当前歌曲ID和名称
        val songId = viewModel.currentSong.value?.mediaId?.toLongOrNull() ?: return
        val songName = viewModel.currentSong.value?.mediaMetadata?.title?.toString() ?: ""

        // 使用Navigation导航到CommentFragment
        val action = PlayerFragmentDirections.actionPlayerFragmentToCommentFragment(
            songId = songId,
            songName = songName
        )
        findNavController().navigate(action)
    }

    /**
     * 显示心动模式
     * 使用Navigation导航到IntelligenceFragment
     */
    private fun showHeartModeDialog() {
        // 检查当前是否有歌曲在播放
        if (viewModel.currentSong.value == null) {
            Toast.makeText(requireContext(), "请先播放一首歌曲", Toast.LENGTH_SHORT).show()
            return
        }

        // 获取当前歌曲ID
        val songId = viewModel.currentSong.value?.mediaId?.toLongOrNull() ?: return

        // 使用Navigation导航到IntelligenceFragment
        val action = PlayerFragmentDirections.actionPlayerFragmentToIntelligenceFragment(
            songId = songId,
            playlistId = -1L
        )
        findNavController().navigate(action)
    }



    /**
     * 设置对话框动画效果
     */
    private fun setupDialogAnimation(dialog: BottomSheetDialog) {
        // 获取对话框内容视图
        val bottomSheet = dialog.findViewById<View>(com.google.android.material.R.id.design_bottom_sheet)
        bottomSheet?.let {
            // 设置初始状态
            it.alpha = 0f
            it.scaleX = 0.8f
            it.scaleY = 0.8f

            // 创建动画
            it.animate()
                .alpha(1f)
                .scaleX(1f)
                .scaleY(1f)
                .setDuration(250)
                .setInterpolator(android.view.animation.OvershootInterpolator(0.8f))
                .start()

            // 设置对话框消失时的动画
            dialog.setOnDismissListener {
                bottomSheet.animate()
                    .alpha(0f)
                    .scaleX(0.8f)
                    .scaleY(0.8f)
                    .setDuration(200)
                    .start()
            }
        }
    }

    /**
     * 显示错误提示
     */
    private fun showErrorSnackbar(message: String) {
        try {
            Snackbar.make(binding.root, message, Snackbar.LENGTH_SHORT)
                .setBackgroundTint(ContextCompat.getColor(requireContext(), R.color.color_error))
                .setTextColor(ContextCompat.getColor(requireContext(), R.color.text_light))
                .show()
        } catch (e: Exception) {
            Log.e(TAG, "显示错误提示失败", e)
        }
    }

    /**
     * 显示错误弹窗
     */
    private fun showErrorDialog(title: String, message: String) {
        try {
            if (isAdded && !requireActivity().isFinishing) {
                AlertDialog.Builder(requireContext())
                    .setTitle(title)
                    .setMessage(message)
                    .setPositiveButton("确定") { dialog, _ ->
                        dialog.dismiss()
                    }
                    .setCancelable(true)
                    .show()
            }
        } catch (e: Exception) {
            Log.e(TAG, "显示错误弹窗失败", e)
        }
    }

    /**
     * 优化触摸反馈
     */
    private fun addTouchFeedback(view: View) {
        view.setOnTouchListener { v, event ->
            when (event.action) {
                MotionEvent.ACTION_DOWN -> {
                    // 按下时缩放
                    v.animate()
                        .scaleX(0.95f)
                        .scaleY(0.95f)
                        .setDuration(100)
                        .start()

                    // 触觉反馈
                    if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                        val vibrator = requireActivity().getSystemService(Context.VIBRATOR_SERVICE) as Vibrator
                        vibrator.vibrate(VibrationEffect.createOneShot(10, VibrationEffect.DEFAULT_AMPLITUDE))
                    }
                }
                MotionEvent.ACTION_UP, MotionEvent.ACTION_CANCEL -> {
                    // 释放时恢复
                    v.animate()
                        .scaleX(1f)
                        .scaleY(1f)
                        .setDuration(100)
                        .start()
                }
            }
            false
        }
    }

    override fun onDestroyView() {
        super.onDestroyView()

        try {
            // 取消颜色动画
            backgroundColorAnimator?.cancel()
            backgroundColorAnimator = null

            // 取消专辑旋转动画
            albumRotationAnimator?.cancel()
            albumRotationAnimator = null

            // 取消歌曲切换动画
            songTransitionAnimator?.cancel()
            songTransitionAnimator = null

            // 清理缓存
            lifecycleScope.launch {
                try {
                    enhancedImageCache.cleanExpiredCache()
                } catch (e: Exception) {
                    Log.e(TAG, "清理缓存失败", e)
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "销毁视图时发生错误", e)
        } finally {
            _binding = null
        }
    }
}
