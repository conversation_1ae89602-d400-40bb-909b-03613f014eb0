package com.example.aimusicplayer.viewmodel;

import android.app.Application;
import com.example.aimusicplayer.data.repository.MusicRepository;
import com.example.aimusicplayer.data.repository.UserRepository;
import com.example.aimusicplayer.error.GlobalErrorHandler;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class ExampleViewModel_Factory implements Factory<ExampleViewModel> {
  private final Provider<Application> applicationProvider;

  private final Provider<MusicRepository> musicRepositoryProvider;

  private final Provider<UserRepository> userRepositoryProvider;

  private final Provider<GlobalErrorHandler> errorHandlerProvider;

  public ExampleViewModel_Factory(Provider<Application> applicationProvider,
      Provider<MusicRepository> musicRepositoryProvider,
      Provider<UserRepository> userRepositoryProvider,
      Provider<GlobalErrorHandler> errorHandlerProvider) {
    this.applicationProvider = applicationProvider;
    this.musicRepositoryProvider = musicRepositoryProvider;
    this.userRepositoryProvider = userRepositoryProvider;
    this.errorHandlerProvider = errorHandlerProvider;
  }

  @Override
  public ExampleViewModel get() {
    return newInstance(applicationProvider.get(), musicRepositoryProvider.get(), userRepositoryProvider.get(), errorHandlerProvider.get());
  }

  public static ExampleViewModel_Factory create(Provider<Application> applicationProvider,
      Provider<MusicRepository> musicRepositoryProvider,
      Provider<UserRepository> userRepositoryProvider,
      Provider<GlobalErrorHandler> errorHandlerProvider) {
    return new ExampleViewModel_Factory(applicationProvider, musicRepositoryProvider, userRepositoryProvider, errorHandlerProvider);
  }

  public static ExampleViewModel newInstance(Application application,
      MusicRepository musicRepository, UserRepository userRepository,
      GlobalErrorHandler errorHandler) {
    return new ExampleViewModel(application, musicRepository, userRepository, errorHandler);
  }
}
