package com.example.aimusicplayer.data.db.dao;

import android.database.Cursor;
import android.os.CancellationSignal;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.room.CoroutinesRoom;
import androidx.room.EntityDeletionOrUpdateAdapter;
import androidx.room.EntityInsertionAdapter;
import androidx.room.RoomDatabase;
import androidx.room.RoomSQLiteQuery;
import androidx.room.SharedSQLiteStatement;
import androidx.room.util.CursorUtil;
import androidx.room.util.DBUtil;
import androidx.sqlite.db.SupportSQLiteStatement;
import com.example.aimusicplayer.data.db.entity.PlayHistoryEntity;
import com.example.aimusicplayer.data.db.entity.SongEntity;
import java.lang.Class;
import java.lang.Exception;
import java.lang.Long;
import java.lang.Object;
import java.lang.Override;
import java.lang.String;
import java.lang.SuppressWarnings;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.Callable;
import javax.annotation.processing.Generated;
import kotlin.Unit;
import kotlin.coroutines.Continuation;
import kotlinx.coroutines.flow.Flow;

@Generated("androidx.room.RoomProcessor")
@SuppressWarnings({"unchecked", "deprecation"})
public final class PlayHistoryDao_Impl implements PlayHistoryDao {
  private final RoomDatabase __db;

  private final EntityInsertionAdapter<PlayHistoryEntity> __insertionAdapterOfPlayHistoryEntity;

  private final EntityDeletionOrUpdateAdapter<PlayHistoryEntity> __deletionAdapterOfPlayHistoryEntity;

  private final SharedSQLiteStatement __preparedStmtOfClearAll;

  public PlayHistoryDao_Impl(@NonNull final RoomDatabase __db) {
    this.__db = __db;
    this.__insertionAdapterOfPlayHistoryEntity = new EntityInsertionAdapter<PlayHistoryEntity>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "INSERT OR REPLACE INTO `play_history` (`id`,`song_unique_id`,`play_time`,`play_position`,`play_duration`,`is_completed`,`play_source`,`user_id`) VALUES (nullif(?, 0),?,?,?,?,?,?,?)";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement,
          @NonNull final PlayHistoryEntity entity) {
        statement.bindLong(1, entity.getId());
        statement.bindString(2, entity.getSongUniqueId());
        statement.bindLong(3, entity.getPlayTime());
        statement.bindLong(4, entity.getPlayPosition());
        statement.bindLong(5, entity.getPlayDuration());
        final int _tmp = entity.isCompleted() ? 1 : 0;
        statement.bindLong(6, _tmp);
        statement.bindString(7, entity.getPlaySource());
        statement.bindString(8, entity.getUserId());
      }
    };
    this.__deletionAdapterOfPlayHistoryEntity = new EntityDeletionOrUpdateAdapter<PlayHistoryEntity>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "DELETE FROM `play_history` WHERE `id` = ?";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement,
          @NonNull final PlayHistoryEntity entity) {
        statement.bindLong(1, entity.getId());
      }
    };
    this.__preparedStmtOfClearAll = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "DELETE FROM play_history";
        return _query;
      }
    };
  }

  @Override
  public Object insert(final PlayHistoryEntity history,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        __db.beginTransaction();
        try {
          __insertionAdapterOfPlayHistoryEntity.insert(history);
          __db.setTransactionSuccessful();
          return Unit.INSTANCE;
        } finally {
          __db.endTransaction();
        }
      }
    }, $completion);
  }

  @Override
  public Object delete(final PlayHistoryEntity history,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        __db.beginTransaction();
        try {
          __deletionAdapterOfPlayHistoryEntity.handle(history);
          __db.setTransactionSuccessful();
          return Unit.INSTANCE;
        } finally {
          __db.endTransaction();
        }
      }
    }, $completion);
  }

  @Override
  public Object clearAll(final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        final SupportSQLiteStatement _stmt = __preparedStmtOfClearAll.acquire();
        try {
          __db.beginTransaction();
          try {
            _stmt.executeUpdateDelete();
            __db.setTransactionSuccessful();
            return Unit.INSTANCE;
          } finally {
            __db.endTransaction();
          }
        } finally {
          __preparedStmtOfClearAll.release(_stmt);
        }
      }
    }, $completion);
  }

  @Override
  public Flow<List<PlayHistoryEntity>> getAllPlayHistory() {
    final String _sql = "SELECT * FROM play_history ORDER BY play_time DESC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    return CoroutinesRoom.createFlow(__db, false, new String[] {"play_history"}, new Callable<List<PlayHistoryEntity>>() {
      @Override
      @NonNull
      public List<PlayHistoryEntity> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfSongUniqueId = CursorUtil.getColumnIndexOrThrow(_cursor, "song_unique_id");
          final int _cursorIndexOfPlayTime = CursorUtil.getColumnIndexOrThrow(_cursor, "play_time");
          final int _cursorIndexOfPlayPosition = CursorUtil.getColumnIndexOrThrow(_cursor, "play_position");
          final int _cursorIndexOfPlayDuration = CursorUtil.getColumnIndexOrThrow(_cursor, "play_duration");
          final int _cursorIndexOfIsCompleted = CursorUtil.getColumnIndexOrThrow(_cursor, "is_completed");
          final int _cursorIndexOfPlaySource = CursorUtil.getColumnIndexOrThrow(_cursor, "play_source");
          final int _cursorIndexOfUserId = CursorUtil.getColumnIndexOrThrow(_cursor, "user_id");
          final List<PlayHistoryEntity> _result = new ArrayList<PlayHistoryEntity>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final PlayHistoryEntity _item;
            final long _tmpId;
            _tmpId = _cursor.getLong(_cursorIndexOfId);
            final String _tmpSongUniqueId;
            _tmpSongUniqueId = _cursor.getString(_cursorIndexOfSongUniqueId);
            final long _tmpPlayTime;
            _tmpPlayTime = _cursor.getLong(_cursorIndexOfPlayTime);
            final long _tmpPlayPosition;
            _tmpPlayPosition = _cursor.getLong(_cursorIndexOfPlayPosition);
            final long _tmpPlayDuration;
            _tmpPlayDuration = _cursor.getLong(_cursorIndexOfPlayDuration);
            final boolean _tmpIsCompleted;
            final int _tmp;
            _tmp = _cursor.getInt(_cursorIndexOfIsCompleted);
            _tmpIsCompleted = _tmp != 0;
            final String _tmpPlaySource;
            _tmpPlaySource = _cursor.getString(_cursorIndexOfPlaySource);
            final String _tmpUserId;
            _tmpUserId = _cursor.getString(_cursorIndexOfUserId);
            _item = new PlayHistoryEntity(_tmpId,_tmpSongUniqueId,_tmpPlayTime,_tmpPlayPosition,_tmpPlayDuration,_tmpIsCompleted,_tmpPlaySource,_tmpUserId);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public Flow<List<SongEntity>> getRecentPlayedSongs(final int limit) {
    final String _sql = "SELECT s.* FROM songs s INNER JOIN play_history h ON s.unique_id = h.song_unique_id ORDER BY h.play_time DESC LIMIT ?";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    _statement.bindLong(_argIndex, limit);
    return CoroutinesRoom.createFlow(__db, true, new String[] {"songs",
        "play_history"}, new Callable<List<SongEntity>>() {
      @Override
      @NonNull
      public List<SongEntity> call() throws Exception {
        __db.beginTransaction();
        try {
          final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
          try {
            final int _cursorIndexOfType = CursorUtil.getColumnIndexOrThrow(_cursor, "type");
            final int _cursorIndexOfSongId = CursorUtil.getColumnIndexOrThrow(_cursor, "song_id");
            final int _cursorIndexOfTitle = CursorUtil.getColumnIndexOrThrow(_cursor, "title");
            final int _cursorIndexOfArtist = CursorUtil.getColumnIndexOrThrow(_cursor, "artist");
            final int _cursorIndexOfArtistId = CursorUtil.getColumnIndexOrThrow(_cursor, "artist_id");
            final int _cursorIndexOfAlbum = CursorUtil.getColumnIndexOrThrow(_cursor, "album");
            final int _cursorIndexOfAlbumId = CursorUtil.getColumnIndexOrThrow(_cursor, "album_id");
            final int _cursorIndexOfAlbumCover = CursorUtil.getColumnIndexOrThrow(_cursor, "album_cover");
            final int _cursorIndexOfDuration = CursorUtil.getColumnIndexOrThrow(_cursor, "duration");
            final int _cursorIndexOfUri = CursorUtil.getColumnIndexOrThrow(_cursor, "uri");
            final int _cursorIndexOfPath = CursorUtil.getColumnIndexOrThrow(_cursor, "path");
            final int _cursorIndexOfFileName = CursorUtil.getColumnIndexOrThrow(_cursor, "file_name");
            final int _cursorIndexOfFileSize = CursorUtil.getColumnIndexOrThrow(_cursor, "file_size");
            final int _cursorIndexOfIsVip = CursorUtil.getColumnIndexOrThrow(_cursor, "is_vip");
            final int _cursorIndexOfIsFavorite = CursorUtil.getColumnIndexOrThrow(_cursor, "is_favorite");
            final int _cursorIndexOfLastPlayedTime = CursorUtil.getColumnIndexOrThrow(_cursor, "last_played_time");
            final int _cursorIndexOfUniqueId = CursorUtil.getColumnIndexOrThrow(_cursor, "unique_id");
            final List<SongEntity> _result = new ArrayList<SongEntity>(_cursor.getCount());
            while (_cursor.moveToNext()) {
              final SongEntity _item;
              final int _tmpType;
              _tmpType = _cursor.getInt(_cursorIndexOfType);
              final long _tmpSongId;
              _tmpSongId = _cursor.getLong(_cursorIndexOfSongId);
              final String _tmpTitle;
              _tmpTitle = _cursor.getString(_cursorIndexOfTitle);
              final String _tmpArtist;
              _tmpArtist = _cursor.getString(_cursorIndexOfArtist);
              final long _tmpArtistId;
              _tmpArtistId = _cursor.getLong(_cursorIndexOfArtistId);
              final String _tmpAlbum;
              _tmpAlbum = _cursor.getString(_cursorIndexOfAlbum);
              final long _tmpAlbumId;
              _tmpAlbumId = _cursor.getLong(_cursorIndexOfAlbumId);
              final String _tmpAlbumCover;
              _tmpAlbumCover = _cursor.getString(_cursorIndexOfAlbumCover);
              final long _tmpDuration;
              _tmpDuration = _cursor.getLong(_cursorIndexOfDuration);
              final String _tmpUri;
              _tmpUri = _cursor.getString(_cursorIndexOfUri);
              final String _tmpPath;
              _tmpPath = _cursor.getString(_cursorIndexOfPath);
              final String _tmpFileName;
              _tmpFileName = _cursor.getString(_cursorIndexOfFileName);
              final long _tmpFileSize;
              _tmpFileSize = _cursor.getLong(_cursorIndexOfFileSize);
              final boolean _tmpIsVip;
              final int _tmp;
              _tmp = _cursor.getInt(_cursorIndexOfIsVip);
              _tmpIsVip = _tmp != 0;
              final boolean _tmpIsFavorite;
              final int _tmp_1;
              _tmp_1 = _cursor.getInt(_cursorIndexOfIsFavorite);
              _tmpIsFavorite = _tmp_1 != 0;
              final long _tmpLastPlayedTime;
              _tmpLastPlayedTime = _cursor.getLong(_cursorIndexOfLastPlayedTime);
              _item = new SongEntity(_tmpType,_tmpSongId,_tmpTitle,_tmpArtist,_tmpArtistId,_tmpAlbum,_tmpAlbumId,_tmpAlbumCover,_tmpDuration,_tmpUri,_tmpPath,_tmpFileName,_tmpFileSize,_tmpIsVip,_tmpIsFavorite,_tmpLastPlayedTime);
              final String _tmpUniqueId;
              _tmpUniqueId = _cursor.getString(_cursorIndexOfUniqueId);
              _item.setUniqueId(_tmpUniqueId);
              _result.add(_item);
            }
            __db.setTransactionSuccessful();
            return _result;
          } finally {
            _cursor.close();
          }
        } finally {
          __db.endTransaction();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public Object getLastPlayedSongId(final Continuation<? super String> $completion) {
    final String _sql = "SELECT song_unique_id FROM play_history ORDER BY play_time DESC LIMIT 1";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<String>() {
      @Override
      @Nullable
      public String call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final String _result;
          if (_cursor.moveToFirst()) {
            if (_cursor.isNull(0)) {
              _result = null;
            } else {
              _result = _cursor.getString(0);
            }
          } else {
            _result = null;
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @Override
  public Object getLastPlayPosition(final String songUniqueId,
      final Continuation<? super Long> $completion) {
    final String _sql = "SELECT play_position FROM play_history WHERE song_unique_id = ? ORDER BY play_time DESC LIMIT 1";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    _statement.bindString(_argIndex, songUniqueId);
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<Long>() {
      @Override
      @Nullable
      public Long call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final Long _result;
          if (_cursor.moveToFirst()) {
            if (_cursor.isNull(0)) {
              _result = null;
            } else {
              _result = _cursor.getLong(0);
            }
          } else {
            _result = null;
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @NonNull
  public static List<Class<?>> getRequiredConverters() {
    return Collections.emptyList();
  }
}
