@echo off
echo 快速编译测试...
echo.

echo [1/3] 清理项目...
call gradlew clean --quiet
if %errorlevel% neq 0 (
    echo 错误: 清理失败
    exit /b 1
)
echo ✅ 清理完成

echo [2/3] 编译Kotlin代码...
call gradlew compileDebugKotlin --stacktrace
if %errorlevel% neq 0 (
    echo 错误: Kotlin编译失败
    exit /b 1
)
echo ✅ Kotlin编译成功

echo [3/3] 编译Java代码...
call gradlew compileDebugJavaWithJavac --stacktrace
if %errorlevel% neq 0 (
    echo 错误: Java编译失败
    exit /b 1
)
echo ✅ Java编译成功

echo.
echo 🎉 所有编译测试通过！
