                                                                                                    	at android.os.Parcel.readException(Parcel.java:2978)
                                                                                                    	at android.os.Parcel.readException(Parcel.java:2920)
                                                                                                    	at android.os.IPowerManager$Stub$Proxy.acquireWakeLock(IPowerManager.java:1096)
                                                                                                    	at android.os.PowerManager$WakeLock.acquireLocked(PowerManager.java:3025)
                                                                                                    	at android.os.PowerManager$WakeLock.acquire(PowerManager.java:2991)
                                                                                                    	at androidx.media3.exoplayer.WakeLockManager.updateWakeLock(WakeLockManager.java:96)
                                                                                                    	at androidx.media3.exoplayer.WakeLockManager.setStayAwake(WakeLockManager.java:83)
                                                                                                    	at androidx.media3.exoplayer.ExoPlayerImpl.updateWakeAndWifiLock(ExoPlayerImpl.java:2814)
                                                                                                    	at androidx.media3.exoplayer.ExoPlayerImpl.updatePlaybackInfo(ExoPlayerImpl.java:2028)
                                                                                                    	at androidx.media3.exoplayer.ExoPlayerImpl.setMediaSourcesInternal(ExoPlayerImpl.java:2343)
                                                                                                    	at androidx.media3.exoplayer.ExoPlayerImpl.setMediaSources(ExoPlayerImpl.java:619)
                                                                                                    	at androidx.media3.exoplayer.ExoPlayerImpl.setMediaItems(ExoPlayerImpl.java:582)
                                                                                                    	at androidx.media3.common.BasePlayer.setMediaItems(BasePlayer.java:56)
                                                                                                    	at com.example.aimusicplayer.service.PlayerControllerImpl.replaceAll(PlayerControllerImpl.kt:129)
                                                                                                    	at com.example.aimusicplayer.viewmodel.PlayerViewModel$loadNewSongs$2.invokeSuspend(PlayerViewModel.kt:444)
                                                                                                    	at kotlin.coroutines.jvm.internal.BaseContinuationImpl.resumeWith(ContinuationImpl.kt:33)
                                                                                                    	at kotlinx.coroutines.DispatchedTask.run(DispatchedTask.kt:108)
                                                                                                    	at android.os.Handler.handleCallback(Handler.java:942)
                                                                                                    	at android.os.Handler.dispatchMessage(Handler.java:99)
                                                                                                    	at android.os.Looper.loopOnce(Looper.java:201)
                                                                                                    	at android.os.Looper.loop(Looper.java:288)
                                                                                                    	at android.app.ActivityThread.main(ActivityThread.java:7924)
                                                                                                    	at java.lang.reflect.Method.invoke(Native Method)
                                                                                                    	at com.android.internal.os.RuntimeInit$MethodAndArgsCaller.run(RuntimeInit.java:548)
                                                                                                    	at com.android.internal.os.ZygoteInit.main(ZygoteInit.java:936)
2025-05-24 13:24:36.793  9733-9733  GlobalErrorHandler      com.example.aimusicplayer            E  处理错误 (Ask Gemini)
                                                                                                    java.lang.SecurityException: Neither user 1010208 nor current process has android.permission.WAKE_LOCK.
                                                                                                    	at android.os.Parcel.createExceptionOrNull(Parcel.java:3011)
                                                                                                    	at android.os.Parcel.createException(Parcel.java:2995)
                                                                                                    	at android.os.Parcel.readException(Parcel.java:2978)
                                                                                                    	at android.os.Parcel.readException(Parcel.java:2920)
                                                                                                    	at android.os.IPowerManager$Stub$Proxy.acquireWakeLock(IPowerManager.java:1096)
                                                                                                    	at android.os.PowerManager$WakeLock.acquireLocked(PowerManager.java:3025)
                                                                                                    	at android.os.PowerManager$WakeLock.acquire(PowerManager.java:2991)
                                                                                                    	at androidx.media3.exoplayer.WakeLockManager.updateWakeLock(WakeLockManager.java:96)
                                                                                                    	at androidx.media3.exoplayer.WakeLockManager.setStayAwake(WakeLockManager.java:83)
                                                                                                    	at androidx.media3.exoplayer.ExoPlayerImpl.updateWakeAndWifiLock(ExoPlayerImpl.java:2814)
                                                                                                    	at androidx.media3.exoplayer.ExoPlayerImpl.updatePlaybackInfo(ExoPlayerImpl.java:2028)
                                                                                                    	at androidx.media3.exoplayer.ExoPlayerImpl.setMediaSourcesInternal(ExoPlayerImpl.java:2343)
                                                                                                    	at androidx.media3.exoplayer.ExoPlayerImpl.setMediaSources(ExoPlayerImpl.java:619)
                                                                                                    	at androidx.media3.exoplayer.ExoPlayerImpl.setMediaItems(ExoPlayerImpl.java:582)
                                                                                                    	at androidx.media3.common.BasePlayer.setMediaItems(BasePlayer.java:56)
                                                                                                    	at com.example.aimusicplayer.service.PlayerControllerImpl.replaceAll(PlayerControllerImpl.kt:129)
                                                                                                    	at com.example.aimusicplayer.viewmodel.PlayerViewModel$loadNewSongs$2.invokeSuspend(PlayerViewModel.kt:444)
                                                                                                    	at kotlin.coroutines.jvm.internal.BaseContinuationImpl.resumeWith(ContinuationImpl.kt:33)
                                                                                                    	at kotlinx.coroutines.DispatchedTask.run(DispatchedTask.kt:108)
                                                                                                    	at android.os.Handler.handleCallback(Handler.java:942)
                                                                                                    	at android.os.Handler.dispatchMessage(Handler.java:99)
                                                                                                    	at android.os.Looper.loopOnce(Looper.java:201)
                                                                                                    	at android.os.Looper.loop(Looper.java:288)
                                                                                                    	at android.app.ActivityThread.main(ActivityThread.java:7924)
                                                                                                    	at java.lang.reflect.Method.invoke(Native Method)
                                                                                                    	at com.android.internal.os.RuntimeInit$MethodAndArgsCaller.run(RuntimeInit.java:548)
                                                                                                    	at com.android.internal.os.ZygoteInit.main(ZygoteInit.java:936)
2025-05-24 13:24:36.830  9733-9733  PlayerViewModel         com.example.aimusicplayer            E  加载新歌速递失败 (Ask Gemini)
                                                                                                    java.lang.SecurityException: Neither user 1010208 nor current process has android.permission.WAKE_LOCK.
                                                                                                    	at android.os.Parcel.createExceptionOrNull(Parcel.java:3011)
                                                                                                    	at android.os.Parcel.createException(Parcel.java:2995)
                                                                                                    	at android.os.Parcel.readException(Parcel.java:2978)
                                                                                                    	at android.os.Parcel.readException(Parcel.java:2920)
                                                                                                    	at android.os.IPowerManager$Stub$Proxy.acquireWakeLock(IPowerManager.java:1096)
                                                                                                    	at android.os.PowerManager$WakeLock.acquireLocked(PowerManager.java:3025)
                                                                                                    	at android.os.PowerManager$WakeLock.acquire(PowerManager.java:2991)
                                                                                                    	at androidx.media3.exoplayer.WakeLockManager.updateWakeLock(WakeLockManager.java:96)
                                                                                                    	at androidx.media3.exoplayer.WakeLockManager.setStayAwake(WakeLockManager.java:83)
                                                                                                    	at androidx.media3.exoplayer.ExoPlayerImpl.updateWakeAndWifiLock(ExoPlayerImpl.java:2814)
                                                                                                    	at androidx.media3.exoplayer.ExoPlayerImpl.updatePlaybackInfo(ExoPlayerImpl.java:2028)
                                                                                                    	at androidx.media3.exoplayer.ExoPlayerImpl.setMediaSourcesInternal(ExoPlayerImpl.java:2343)
                                                                                                    	at androidx.media3.exoplayer.ExoPlayerImpl.setMediaSources(ExoPlayerImpl.java:619)
                                                                                                    	at androidx.media3.exoplayer.ExoPlayerImpl.setMediaItems(ExoPlayerImpl.java:582)
                                                                                                    	at androidx.media3.common.BasePlayer.setMediaItems(BasePlayer.java:56)
                                                                                                    	at com.example.aimusicplayer.service.PlayerControllerImpl.replaceAll(PlayerControllerImpl.kt:129)
                                                                                                    	at com.example.aimusicplayer.viewmodel.PlayerViewModel$loadNewSongs$2.invokeSuspend(PlayerViewModel.kt:444)
                                                                                                    	at kotlin.coroutines.jvm.internal.BaseContinuationImpl.resumeWith(ContinuationImpl.kt:33)
                                                                                                    	at kotlinx.coroutines.DispatchedTask.run(DispatchedTask.kt:108)
                                                                                                    	at android.os.Handler.handleCallback(Handler.java:942)
                                                                                                    	at android.os.Handler.dispatchMessage(Handler.java:99)
                                                                                                    	at android.os.Looper.loopOnce(Looper.java:201)
                                                                                                    	at android.os.Looper.loop(Looper.java:288)
                                                                                                    	at android.app.ActivityThread.main(ActivityThread.java:7924)
                                                                                                    	at java.lang.reflect.Method.invoke(Native Method)
                                                                                                    	at com.android.internal.os.RuntimeInit$MethodAndArgsCaller.run(RuntimeInit.java:548)
                                                                                                    	at com.android.internal.os.ZygoteInit.main(ZygoteInit.java:936)
2025-05-24 13:24:36.842  9733-9733  FlowViewModel           com.example.aimusicplayer            E  Error in ViewModel (Ask Gemini)
                                                                                                    java.lang.SecurityException: Neither user 1010208 nor current process has android.permission.WAKE_LOCK.
                                                                                                    	at android.os.Parcel.createExceptionOrNull(Parcel.java:3011)
                                                                                                    	at android.os.Parcel.createException(Parcel.java:2995)
                                                                                                    	at android.os.Parcel.readException(Parcel.java:2978)
                                                                                                    	at android.os.Parcel.readException(Parcel.java:2920)
                                                                                                    	at android.os.IPowerManager$Stub$Proxy.acquireWakeLock(IPowerManager.java:1096)
                                                                                                    	at android.os.PowerManager$WakeLock.acquireLocked(PowerManager.java:3025)
                                                                                                    	at android.os.PowerManager$WakeLock.acquire(PowerManager.java:2991)
                                                                                                    	at androidx.media3.exoplayer.WakeLockManager.updateWakeLock(WakeLockManager.java:96)
                                                                                                    	at androidx.media3.exoplayer.WakeLockManager.setStayAwake(WakeLockManager.java:83)
                                                                                                    	at androidx.media3.exoplayer.ExoPlayerImpl.updateWakeAndWifiLock(ExoPlayerImpl.java:2814)
                                                                                                    	at androidx.media3.exoplayer.ExoPlayerImpl.updatePlaybackInfo(ExoPlayerImpl.java:2028)
                                                                                                    	at androidx.media3.exoplayer.ExoPlayerImpl.setMediaSourcesInternal(ExoPlayerImpl.java:2343)
                                                                                                    	at androidx.media3.exoplayer.ExoPlayerImpl.setMediaSources(ExoPlayerImpl.java:619)
                                                                                                    	at androidx.media3.exoplayer.ExoPlayerImpl.setMediaItems(ExoPlayerImpl.java:582)
                                                                                                    	at androidx.media3.common.BasePlayer.setMediaItems(BasePlayer.java:56)
                                                                                                    	at com.example.aimusicplayer.service.PlayerControllerImpl.replaceAll(PlayerControllerImpl.kt:129)
                                                                                                    	at com.example.aimusicplayer.viewmodel.PlayerViewModel$loadNewSongs$2.invokeSuspend(PlayerViewModel.kt:444)
                                                                                                    	at kotlin.coroutines.jvm.internal.BaseContinuationImpl.resumeWith(ContinuationImpl.kt:33)
                                                                                                    	at kotlinx.coroutines.DispatchedTask.run(DispatchedTask.kt:108)
                                                                                                    	at android.os.Handler.handleCallback(Handler.java:942)
                                                                                                    	at android.os.Handler.dispatchMessage(Handler.java:99)
                                                                                                    	at android.os.Looper.loopOnce(Looper.java:201)
                                                                                                    	at android.os.Looper.loop(Looper.java:288)
                                                                                                    	at android.app.ActivityThread.main(ActivityThread.java:7924)
                                                                                                    	at java.lang.reflect.Method.invoke(Native Method)
                                                                                                    	at com.android.internal.os.RuntimeInit$MethodAndArgsCaller.run(RuntimeInit.java:548)
                                                                                                    	at com.android.internal.os.ZygoteInit.main(ZygoteInit.java:936)
2025-05-24 13:24:36.843  9733-9733  GlobalErrorHandler      com.example.aimusicplayer            E  处理错误 (Ask Gemini)
                                                                                                    java.lang.SecurityException: Neither user 1010208 nor current process has android.permission.WAKE_LOCK.
                                                                                                    	at android.os.Parcel.createExceptionOrNull(Parcel.java:3011)
                                                                                                    	at android.os.Parcel.createException(Parcel.java:2995)
                                                                                                    	at android.os.Parcel.readException(Parcel.java:2978)
                                                                                                    	at android.os.Parcel.readException(Parcel.java:2920)
                                                                                                    	at android.os.IPowerManager$Stub$Proxy.acquireWakeLock(IPowerManager.java:1096)
                                                                                                    	at android.os.PowerManager$WakeLock.acquireLocked(PowerManager.java:3025)
                                                                                                    	at android.os.PowerManager$WakeLock.acquire(PowerManager.java:2991)
                                                                                                    	at androidx.media3.exoplayer.WakeLockManager.updateWakeLock(WakeLockManager.java:96)
                                                                                                    	at androidx.media3.exoplayer.WakeLockManager.setStayAwake(WakeLockManager.java:83)
                                                                                                    	at androidx.media3.exoplayer.ExoPlayerImpl.updateWakeAndWifiLock(ExoPlayerImpl.java:2814)
                                                                                                    	at androidx.media3.exoplayer.ExoPlayerImpl.updatePlaybackInfo(ExoPlayerImpl.java:2028)
                                                                                                    	at androidx.media3.exoplayer.ExoPlayerImpl.setMediaSourcesInternal(ExoPlayerImpl.java:2343)
                                                                                                    	at androidx.media3.exoplayer.ExoPlayerImpl.setMediaSources(ExoPlayerImpl.java:619)
                                                                                                    	at androidx.media3.exoplayer.ExoPlayerImpl.setMediaItems(ExoPlayerImpl.java:582)
                                                                                                    	at androidx.media3.common.BasePlayer.setMediaItems(BasePlayer.java:56)
                                                                                                    	at com.example.aimusicplayer.service.PlayerControllerImpl.replaceAll(PlayerControllerImpl.kt:129)
                                                                                                    	at com.example.aimusicplayer.viewmodel.PlayerViewModel$loadNewSongs$2.invokeSuspend(PlayerViewModel.kt:444)
                                                                                                    	at kotlin.coroutines.jvm.internal.BaseContinuationImpl.resumeWith(ContinuationImpl.kt:33)
                                                                                                    	at kotlinx.coroutines.DispatchedTask.run(DispatchedTask.kt:108)
                                                                                                    	at android.os.Handler.handleCallback(Handler.java:942)
                                                                                                    	at android.os.Handler.dispatchMessage(Handler.java:99)
                                                                                                    	at android.os.Looper.loopOnce(Looper.java:201)
                                                                                                    	at android.os.Looper.loop(Looper.java:288)
                                                                                                    	at android.app.ActivityThread.main(ActivityThread.java:7924)
                                                                                                    	at java.lang.reflect.Method.invoke(Native Method)
                                                                                                    	at com.android.internal.os.RuntimeInit$MethodAndArgsCaller.run(RuntimeInit.java:548)
                                                                                                    	at com.android.internal.os.ZygoteInit.main(ZygoteInit.java:936)
2025-05-24 13:24:37.447  9733-9762  ExoPlayerImplInternal   com.example.aimusicplayer            E  Playback error (Ask Gemini)
                                                                                                      androidx.media3.exoplayer.ExoPlaybackException: Source error
                                                                                                          at androidx.media3.exoplayer.ExoPlayerImplInternal.handleIoException(ExoPlayerImplInternal.java:701)
                                                                                                          at androidx.media3.exoplayer.ExoPlayerImplInternal.handleMessage(ExoPlayerImplInternal.java:671)
                                                                                                          at android.os.Handler.dispatchMessage(Handler.java:102)
                                                                                                          at android.os.Looper.loopOnce(Looper.java:201)
                                                                                                          at android.os.Looper.loop(Looper.java:288)
                                                                                                          at android.os.HandlerThread.run(HandlerThread.java:67)
                                                                                                      Caused by: androidx.media3.exoplayer.source.UnrecognizedInputFormatException: None of the available extractors (FlvExtractor, FlacExtractor, WavExtractor, FragmentedMp4Extractor, Mp4Extractor, AmrExtractor, PsExtractor, OggExtractor, TsExtractor, MatroskaExtractor, AdtsExtractor, Ac3Extractor, Ac4Extractor, Mp3Extractor, AviExtractor, JpegExtractor, PngExtractor, WebpExtractor, BmpExtractor, HeifExtractor) could read the stream.{contentIsMalformed=false, dataType=1}
                                                                                                          at androidx.media3.exoplayer.source.BundledExtractorsAdapter.init(BundledExtractorsAdapter.java:94)
                                                                                                          at androidx.media3.exoplayer.source.ProgressiveMediaPeriod$ExtractingLoadable.load(ProgressiveMediaPeriod.java:1044)
                                                                                                          at androidx.media3.exoplayer.upstream.Loader$LoadTask.run(Loader.java:417)
                                                                                                          at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1137)
                                                                                                          at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:637)
                                                                                                          at java.lang.Thread.run(Thread.java:1012)
2025-05-24 13:24:37.462  9733-9733  UnifiedPlaybackService  com.example.aimusicplayer            E  播放错误: ERROR_CODE_PARSING_CONTAINER_UNSUPPORTED, Source error
2025-05-24 13:24:37.752  9733-9809  EnhancedImageCache      com.example.aimusicplayer            E  使用Glide加载图片失败 (Ask Gemini)
                                                                                                    java.util.concurrent.ExecutionException: com.bumptech.glide.load.engine.GlideException: Failed to load resource
                                                                                                    	at com.bumptech.glide.request.RequestFutureTarget.doGet(RequestFutureTarget.java:217)
                                                                                                    	at com.bumptech.glide.request.RequestFutureTarget.get(RequestFutureTarget.java:130)
                                                                                                    	at com.example.aimusicplayer.utils.EnhancedImageCache$getBitmap$2.invokeSuspend(EnhancedImageCache.kt:208)
                                                                                                    	at kotlin.coroutines.jvm.internal.BaseContinuationImpl.resumeWith(ContinuationImpl.kt:33)
                                                                                                    	at kotlinx.coroutines.DispatchedTask.run(DispatchedTask.kt:108)
                                                                                                    	at kotlinx.coroutines.internal.LimitedDispatcher$Worker.run(LimitedDispatcher.kt:115)
                                                                                                    	at kotlinx.coroutines.scheduling.TaskImpl.run(Tasks.kt:103)
                                                                                                    	at kotlinx.coroutines.scheduling.CoroutineScheduler.runSafely(CoroutineScheduler.kt:584)
                                                                                                    	at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.executeTask(CoroutineScheduler.kt:793)
                                                                                                    	at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.runWorker(CoroutineScheduler.kt:697)
                                                                                                    	at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.run(CoroutineScheduler.kt:684)
                                                                                                    Caused by: com.bumptech.glide.load.engine.GlideException: Failed to load resource
2025-05-24 13:24:37.755  9733-9810  ImageUtils              com.example.aimusicplayer            E  加载图片失败:  (Ask Gemini)
                                                                                                    java.util.concurrent.ExecutionException: com.bumptech.glide.load.engine.GlideException: Failed to load resource
                                                                                                    	at com.bumptech.glide.request.RequestFutureTarget.doGet(RequestFutureTarget.java:217)
                                                                                                    	at com.bumptech.glide.request.RequestFutureTarget.get(RequestFutureTarget.java:130)
                                                                                                    	at com.example.aimusicplayer.utils.ImageUtils$loadBitmapFromUri$2.invokeSuspend(ImageUtils.kt:255)
                                                                                                    	at com.example.aimusicplayer.utils.ImageUtils$loadBitmapFromUri$2.invoke(Unknown Source:8)
                                                                                                    	at com.example.aimusicplayer.utils.ImageUtils$loadBitmapFromUri$2.invoke(Unknown Source:4)
                                                                                                    	at kotlinx.coroutines.intrinsics.UndispatchedKt.startUndispatchedOrReturn(Undispatched.kt:78)
                                                                                                    	at kotlinx.coroutines.BuildersKt__Builders_commonKt.withContext(Builders.common.kt:167)
                                                                                                    	at kotlinx.coroutines.BuildersKt.withContext(Unknown Source:1)
                                                                                                    	at com.example.aimusicplayer.utils.ImageUtils.loadBitmapFromUri(ImageUtils.kt:249)
                                                                                                    	at com.example.aimusicplayer.utils.ImageUtils$loadAndProcessAlbumCover$2.invokeSuspend(ImageUtils.kt:275)
                                                                                                    	at kotlin.coroutines.jvm.internal.BaseContinuationImpl.resumeWith(ContinuationImpl.kt:33)
                                                                                                    	at kotlinx.coroutines.DispatchedTask.run(DispatchedTask.kt:108)
                                                                                                    	at kotlinx.coroutines.internal.LimitedDispatcher$Worker.run(LimitedDispatcher.kt:115)
                                                                                                    	at kotlinx.coroutines.scheduling.TaskImpl.run(Tasks.kt:103)
                                                                                                    	at kotlinx.coroutines.scheduling.CoroutineScheduler.runSafely(CoroutineScheduler.kt:584)
                                                                                                    	at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.executeTask(CoroutineScheduler.kt:793)
                                                                                                    	at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.runWorker(CoroutineScheduler.kt:697)
                                                                                                    	at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.run(CoroutineScheduler.kt:684)
                                                                                                    Caused by: com.bumptech.glide.load.engine.GlideException: Failed to load resource
2025-05-24 13:24:37.758  9733-9774  ImageUtils              com.example.aimusicplayer            E  加载图片失败:  (Ask Gemini)
                                                                                                    java.util.concurrent.ExecutionException: com.bumptech.glide.load.engine.GlideException: Failed to load resource
                                                                                                    	at com.bumptech.glide.request.RequestFutureTarget.doGet(RequestFutureTarget.java:217)
                                                                                                    	at com.bumptech.glide.request.RequestFutureTarget.get(RequestFutureTarget.java:130)
                                                                                                    	at com.example.aimusicplayer.utils.ImageUtils$loadBitmapFromUri$2.invokeSuspend(ImageUtils.kt:255)
                                                                                                    	at com.example.aimusicplayer.utils.ImageUtils$loadBitmapFromUri$2.invoke(Unknown Source:8)
                                                                                                    	at com.example.aimusicplayer.utils.ImageUtils$loadBitmapFromUri$2.invoke(Unknown Source:4)
                                                                                                    	at kotlinx.coroutines.intrinsics.UndispatchedKt.startUndispatchedOrReturn(Undispatched.kt:78)
                                                                                                    	at kotlinx.coroutines.BuildersKt__Builders_commonKt.withContext(Builders.common.kt:167)
                                                                                                    	at kotlinx.coroutines.BuildersKt.withContext(Unknown Source:1)
                                                                                                    	at com.example.aimusicplayer.utils.ImageUtils.loadBitmapFromUri(ImageUtils.kt:249)
                                                                                                    	at com.example.aimusicplayer.utils.ImageUtils$loadAndProcessAlbumCover$2.invokeSuspend(ImageUtils.kt:275)
                                                                                                    	at kotlin.coroutines.jvm.internal.BaseContinuationImpl.resumeWith(ContinuationImpl.kt:33)
                                                                                                    	at kotlinx.coroutines.DispatchedTask.run(DispatchedTask.kt:108)
                                                                                                    	at kotlinx.coroutines.internal.LimitedDispatcher$Worker.run(LimitedDispatcher.kt:115)
                                                                                                    	at kotlinx.coroutines.scheduling.TaskImpl.run(Tasks.kt:103)
                                                                                                    	at kotlinx.coroutines.scheduling.CoroutineScheduler.runSafely(CoroutineScheduler.kt:584)
                                                                                                    	at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.executeTask(CoroutineScheduler.kt:793)
                                                                                                    	at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.runWorker(CoroutineScheduler.kt:697)
                                                                                                    	at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.run(CoroutineScheduler.kt:684)
                                                                                                    Caused by: com.bumptech.glide.load.engine.GlideException: Failed to load resource
2025-05-24 13:24:37.758  9733-9733  PlayerFragment          com.example.aimusicplayer            E  加载专辑封面失败 (Ask Gemini)
                                                                                                    java.lang.NullPointerException
                                                                                                    	at com.example.aimusicplayer.ui.player.PlayerFragment$setupObservers$1$1$1.invokeSuspend(PlayerFragment.kt:330)
                                                                                                    	at kotlin.coroutines.jvm.internal.BaseContinuationImpl.resumeWith(ContinuationImpl.kt:33)
                                                                                                    	at kotlinx.coroutines.DispatchedTask.run(DispatchedTask.kt:108)
                                                                                                    	at android.os.Handler.handleCallback(Handler.java:942)
                                                                                                    	at android.os.Handler.dispatchMessage(Handler.java:99)
                                                                                                    	at android.os.Looper.loopOnce(Looper.java:201)
                                                                                                    	at android.os.Looper.loop(Looper.java:288)
                                                                                                    	at android.app.ActivityThread.main(ActivityThread.java:7924)
                                                                                                    	at java.lang.reflect.Method.invoke(Native Method)
                                                                                                    	at com.android.internal.os.RuntimeInit$MethodAndArgsCaller.run(RuntimeInit.java:548)
                                                                                                    	at com.android.internal.os.ZygoteInit.main(ZygoteInit.java:936)
2025-05-24 13:24:37.759  9733-9793  ImageUtils              com.example.aimusicplayer            E  加载图片失败:  (Ask Gemini)
                                                                                                    java.util.concurrent.ExecutionException: com.bumptech.glide.load.engine.GlideException: Failed to load resource
                                                                                                    	at com.bumptech.glide.request.RequestFutureTarget.doGet(RequestFutureTarget.java:217)
                                                                                                    	at com.bumptech.glide.request.RequestFutureTarget.get(RequestFutureTarget.java:130)
                                                                                                    	at com.example.aimusicplayer.utils.ImageUtils$loadBitmapFromUri$2.invokeSuspend(ImageUtils.kt:255)
                                                                                                    	at com.example.aimusicplayer.utils.ImageUtils$loadBitmapFromUri$2.invoke(Unknown Source:8)
                                                                                                    	at com.example.aimusicplayer.utils.ImageUtils$loadBitmapFromUri$2.invoke(Unknown Source:4)
                                                                                                    	at kotlinx.coroutines.intrinsics.UndispatchedKt.startUndispatchedOrReturn(Undispatched.kt:78)
                                                                                                    	at kotlinx.coroutines.BuildersKt__Builders_commonKt.withContext(Builders.common.kt:167)
                                                                                                    	at kotlinx.coroutines.BuildersKt.withContext(Unknown Source:1)
                                                                                                    	at com.example.aimusicplayer.utils.ImageUtils.loadBitmapFromUri(ImageUtils.kt:249)
                                                                                                    	at com.example.aimusicplayer.utils.ImageUtils$loadAndProcessAlbumCover$2.invokeSuspend(ImageUtils.kt:275)
                                                                                                    	at kotlin.coroutines.jvm.internal.BaseContinuationImpl.resumeWith(ContinuationImpl.kt:33)
                                                                                                    	at kotlinx.coroutines.DispatchedTask.run(DispatchedTask.kt:108)
                                                                                                    	at kotlinx.coroutines.internal.LimitedDispatcher$Worker.run(LimitedDispatcher.kt:115)
                                                                                                    	at kotlinx.coroutines.scheduling.TaskImpl.run(Tasks.kt:103)
                                                                                                    	at kotlinx.coroutines.scheduling.CoroutineScheduler.runSafely(CoroutineScheduler.kt:584)
                                                                                                    	at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.executeTask(CoroutineScheduler.kt:793)
                                                                                                    	at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.runWorker(CoroutineScheduler.kt:697)
                                                                                                    	at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.run(CoroutineScheduler.kt:684)
                                                                                                    Caused by: com.bumptech.glide.load.engine.GlideException: Failed to load resource
2025-05-24 13:24:37.761  9733-9775  ImageUtils              com.example.aimusicplayer            E  加载图片失败:  (Ask Gemini)
                                                                                                    java.util.concurrent.ExecutionException: com.bumptech.glide.load.engine.GlideException: Failed to load resource
                                                                                                    	at com.bumptech.glide.request.RequestFutureTarget.doGet(RequestFutureTarget.java:217)
                                                                                                    	at com.bumptech.glide.request.RequestFutureTarget.get(RequestFutureTarget.java:130)
                                                                                                    	at com.example.aimusicplayer.utils.ImageUtils$loadBitmapFromUri$2.invokeSuspend(ImageUtils.kt:255)
                                                                                                    	at com.example.aimusicplayer.utils.ImageUtils$loadBitmapFromUri$2.invoke(Unknown Source:8)
                                                                                                    	at com.example.aimusicplayer.utils.ImageUtils$loadBitmapFromUri$2.invoke(Unknown Source:4)
                                                                                                    	at kotlinx.coroutines.intrinsics.UndispatchedKt.startUndispatchedOrReturn(Undispatched.kt:78)
                                                                                                    	at kotlinx.coroutines.BuildersKt__Builders_commonKt.withContext(Builders.common.kt:167)
                                                                                                    	at kotlinx.coroutines.BuildersKt.withContext(Unknown Source:1)
                                                                                                    	at com.example.aimusicplayer.utils.ImageUtils.loadBitmapFromUri(ImageUtils.kt:249)
                                                                                                    	at com.example.aimusicplayer.utils.ImageUtils$loadAndProcessAlbumCover$2.invokeSuspend(ImageUtils.kt:275)
                                                                                                    	at kotlin.coroutines.jvm.internal.BaseContinuationImpl.resumeWith(ContinuationImpl.kt:33)
                                                                                                    	at kotlinx.coroutines.DispatchedTask.run(DispatchedTask.kt:108)
                                                                                                    	at kotlinx.coroutines.internal.LimitedDispatcher$Worker.run(LimitedDispatcher.kt:115)
                                                                                                    	at kotlinx.coroutines.scheduling.TaskImpl.run(Tasks.kt:103)
                                                                                                    	at kotlinx.coroutines.scheduling.CoroutineScheduler.runSafely(CoroutineScheduler.kt:584)
                                                                                                    	at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.executeTask(CoroutineScheduler.kt:793)
                                                                                                    	at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.runWorker(CoroutineScheduler.kt:697)
                                                                                                    	at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.run(CoroutineScheduler.kt:684)
                                                                                                    Caused by: com.bumptech.glide.load.engine.GlideException: Failed to load resource
2025-05-24 13:24:37.767  9733-9811  EnhancedImageCache      com.example.aimusicplayer            E  使用Glide加载图片失败 (Ask Gemini)
                                                                                                    java.util.concurrent.ExecutionException: com.bumptech.glide.load.engine.GlideException: Failed to load resource
                                                                                                    	at com.bumptech.glide.request.RequestFutureTarget.doGet(RequestFutureTarget.java:217)
                                                                                                    	at com.bumptech.glide.request.RequestFutureTarget.get(RequestFutureTarget.java:130)
                                                                                                    	at com.example.aimusicplayer.utils.EnhancedImageCache$getBitmap$2.invokeSuspend(EnhancedImageCache.kt:208)
                                                                                                    	at kotlin.coroutines.jvm.internal.BaseContinuationImpl.resumeWith(ContinuationImpl.kt:33)
                                                                                                    	at kotlinx.coroutines.DispatchedTask.run(DispatchedTask.kt:108)
                                                                                                    	at kotlinx.coroutines.internal.LimitedDispatcher$Worker.run(LimitedDispatcher.kt:115)
                                                                                                    	at kotlinx.coroutines.scheduling.TaskImpl.run(Tasks.kt:103)
                                                                                                    	at kotlinx.coroutines.scheduling.CoroutineScheduler.runSafely(CoroutineScheduler.kt:584)
                                                                                                    	at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.executeTask(CoroutineScheduler.kt:793)
                                                                                                    	at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.runWorker(CoroutineScheduler.kt:697)
                                                                                                    	at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.run(CoroutineScheduler.kt:684)
                                                                                                    Caused by: com.bumptech.glide.load.engine.GlideException: Failed to load resource
2025-05-24 13:24:37.768  9733-9776  ImageUtils              com.example.aimusicplayer            E  加载图片失败:  (Ask Gemini)
                                                                                                    java.util.concurrent.ExecutionException: com.bumptech.glide.load.engine.GlideException: Failed to load resource
                                                                                                    	at com.bumptech.glide.request.RequestFutureTarget.doGet(RequestFutureTarget.java:217)
                                                                                                    	at com.bumptech.glide.request.RequestFutureTarget.get(RequestFutureTarget.java:130)
                                                                                                    	at com.example.aimusicplayer.utils.ImageUtils$loadBitmapFromUri$2.invokeSuspend(ImageUtils.kt:255)
                                                                                                    	at com.example.aimusicplayer.utils.ImageUtils$loadBitmapFromUri$2.invoke(Unknown Source:8)
                                                                                                    	at com.example.aimusicplayer.utils.ImageUtils$loadBitmapFromUri$2.invoke(Unknown Source:4)
                                                                                                    	at kotlinx.coroutines.intrinsics.UndispatchedKt.startUndispatchedOrReturn(Undispatched.kt:78)
                                                                                                    	at kotlinx.coroutines.BuildersKt__Builders_commonKt.withContext(Builders.common.kt:167)
                                                                                                    	at kotlinx.coroutines.BuildersKt.withContext(Unknown Source:1)
                                                                                                    	at com.example.aimusicplayer.utils.ImageUtils.loadBitmapFromUri(ImageUtils.kt:249)
                                                                                                    	at com.example.aimusicplayer.utils.ImageUtils$loadAndProcessAlbumCover$2.invokeSuspend(ImageUtils.kt:275)
                                                                                                    	at kotlin.coroutines.jvm.internal.BaseContinuationImpl.resumeWith(ContinuationImpl.kt:33)
                                                                                                    	at kotlinx.coroutines.DispatchedTask.run(DispatchedTask.kt:108)
                                                                                                    	at kotlinx.coroutines.internal.LimitedDispatcher$Worker.run(LimitedDispatcher.kt:115)
                                                                                                    	at kotlinx.coroutines.scheduling.TaskImpl.run(Tasks.kt:103)
                                                                                                    	at kotlinx.coroutines.scheduling.CoroutineScheduler.runSafely(CoroutineScheduler.kt:584)
                                                                                                    	at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.executeTask(CoroutineScheduler.kt:793)
                                                                                                    	at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.runWorker(CoroutineScheduler.kt:697)
                                                                                                    	at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.run(CoroutineScheduler.kt:684)
                                                                                                    Caused by: com.bumptech.glide.load.engine.GlideException: Failed to load resource
2025-05-24 13:24:37.774  9733-9815  ImageUtils              com.example.aimusicplayer            E  加载图片失败:  (Ask Gemini)
                                                                                                    java.util.concurrent.ExecutionException: com.bumptech.glide.load.engine.GlideException: Failed to load resource
                                                                                                    	at com.bumptech.glide.request.RequestFutureTarget.doGet(RequestFutureTarget.java:217)
                                                                                                    	at com.bumptech.glide.request.RequestFutureTarget.get(RequestFutureTarget.java:130)
                                                                                                    	at com.example.aimusicplayer.utils.ImageUtils$loadBitmapFromUri$2.invokeSuspend(ImageUtils.kt:255)
                                                                                                    	at com.example.aimusicplayer.utils.ImageUtils$loadBitmapFromUri$2.invoke(Unknown Source:8)
                                                                                                    	at com.example.aimusicplayer.utils.ImageUtils$loadBitmapFromUri$2.invoke(Unknown Source:4)
                                                                                                    	at kotlinx.coroutines.intrinsics.UndispatchedKt.startUndispatchedOrReturn(Undispatched.kt:78)
                                                                                                    	at kotlinx.coroutines.BuildersKt__Builders_commonKt.withContext(Builders.common.kt:167)
                                                                                                    	at kotlinx.coroutines.BuildersKt.withContext(Unknown Source:1)
                                                                                                    	at com.example.aimusicplayer.utils.ImageUtils.loadBitmapFromUri(ImageUtils.kt:249)
                                                                                                    	at com.example.aimusicplayer.utils.ImageUtils$loadAndProcessAlbumCover$2.invokeSuspend(ImageUtils.kt:275)
                                                                                                    	at kotlin.coroutines.jvm.internal.BaseContinuationImpl.resumeWith(ContinuationImpl.kt:33)
                                                                                                    	at kotlinx.coroutines.DispatchedTask.run(DispatchedTask.kt:108)
                                                                                                    	at kotlinx.coroutines.internal.LimitedDispatcher$Worker.run(LimitedDispatcher.kt:115)
                                                                                                    	at kotlinx.coroutines.scheduling.TaskImpl.run(Tasks.kt:103)
                                                                                                    	at kotlinx.coroutines.scheduling.CoroutineScheduler.runSafely(CoroutineScheduler.kt:584)
                                                                                                    	at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.executeTask(CoroutineScheduler.kt:793)
                                                                                                    	at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.runWorker(CoroutineScheduler.kt:697)
                                                                                                    	at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.run(CoroutineScheduler.kt:684)
                                                                                                    Caused by: com.bumptech.glide.load.engine.GlideException: Failed to load resource
2025-05-24 13:24:37.780  9733-9733  AndroidRuntime          com.example.aimusicplayer            E  FATAL EXCEPTION: main (Ask Gemini)
                                                                                                    Process: com.example.aimusicplayer, PID: 9733
                                                                                                    java.lang.NullPointerException
                                                                                                    	at com.example.aimusicplayer.ui.player.PlayerFragment$setupObservers$1$1$1.invokeSuspend(PlayerFragment.kt:339)
                                                                                                    	at kotlin.coroutines.jvm.internal.BaseContinuationImpl.resumeWith(ContinuationImpl.kt:33)
                                                                                                    	at kotlinx.coroutines.DispatchedTask.run(DispatchedTask.kt:108)
                                                                                                    	at android.os.Handler.handleCallback(Handler.java:942)
                                                                                                    	at android.os.Handler.dispatchMessage(Handler.java:99)
                                                                                                    	at android.os.Looper.loopOnce(Looper.java:201)
                                                                                                    	at android.os.Looper.loop(Looper.java:288)
                                                                                                    	at android.app.ActivityThread.main(ActivityThread.java:7924)
                                                                                                    	at java.lang.reflect.Method.invoke(Native Method)
                                                                                                    	at com.android.internal.os.RuntimeInit$MethodAndArgsCaller.run(RuntimeInit.java:548)
                                                                                                    	at com.android.internal.os.ZygoteInit.main(ZygoteInit.java:936)
                                                                                                    	Suppressed: kotlinx.coroutines.internal.DiagnosticCoroutineContextException: [StandaloneCoroutine{Cancelling}@95ec02b, Dispatchers.Main.immediate]
2025-05-24 13:24:37.962   572-2398  InputDispatcher         system_server                        E  But another display has a focused window
                                                                                                      FocusedWindows:
                                                                                                        displayId=6, name='6b040bd com.android.systemui/com.android.systemui.car.distantdisplay.activity.RootTaskViewWallpaperActivity'
                                                                                                        displayId=3, name='88e4b79 com.android.systemui/com.android.systemui.car.distantdisplay.activity.DistantDisplayActivity'
                                                                                                        displayId=5, name='3ef21bf com.android.systemui/com.android.systemui.car.distantdisplay.activity.NavigationTaskViewWallpaperActivity'
                                                                                                        displayId=2, name='fb438ae com.android.car.cluster.osdouble/com.android.car.cluster.osdouble.ClusterOsDoubleActivity'
2025-05-24 13:24:38.149  4763-4797  OpenGLRenderer          com.android.car.carlauncher          E  Unable to match the desired swap behavior.
2025-05-24 13:24:38.199   374-374   ClientCache             surfaceflinger                       E  failed to get buffer, invalid process token
2025-05-24 13:24:38.202   374-374   BpTransact...edListener surfaceflinger                       E  Failed to transact (-32)
2025-05-24 13:24:39.687   572-1638  TaskPersister           system_server                        E  File error accessing recents directory (directory doesn't exist?).
2025-05-24 13:24:39.687   572-1638  TaskPersister           system_server                        E  File error accessing recents directory (directory doesn't exist?).
2025-05-24 13:24:39.687   572-1638  TaskPersister           system_server                        E  File error accessing recents directory (directory doesn't exist?).
