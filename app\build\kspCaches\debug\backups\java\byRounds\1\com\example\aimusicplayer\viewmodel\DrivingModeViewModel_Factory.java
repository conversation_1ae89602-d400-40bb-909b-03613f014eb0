package com.example.aimusicplayer.viewmodel;

import android.app.Application;
import com.example.aimusicplayer.data.repository.MusicRepository;
import com.example.aimusicplayer.data.repository.SettingsRepository;
import com.example.aimusicplayer.error.GlobalErrorHandler;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class DrivingModeViewModel_Factory implements Factory<DrivingModeViewModel> {
  private final Provider<Application> applicationProvider;

  private final Provider<MusicRepository> musicRepositoryProvider;

  private final Provider<SettingsRepository> settingsRepositoryProvider;

  private final Provider<GlobalErrorHandler> errorHandlerProvider;

  public DrivingModeViewModel_Factory(Provider<Application> applicationProvider,
      Provider<MusicRepository> musicRepositoryProvider,
      Provider<SettingsRepository> settingsRepositoryProvider,
      Provider<GlobalErrorHandler> errorHandlerProvider) {
    this.applicationProvider = applicationProvider;
    this.musicRepositoryProvider = musicRepositoryProvider;
    this.settingsRepositoryProvider = settingsRepositoryProvider;
    this.errorHandlerProvider = errorHandlerProvider;
  }

  @Override
  public DrivingModeViewModel get() {
    return newInstance(applicationProvider.get(), musicRepositoryProvider.get(), settingsRepositoryProvider.get(), errorHandlerProvider.get());
  }

  public static DrivingModeViewModel_Factory create(Provider<Application> applicationProvider,
      Provider<MusicRepository> musicRepositoryProvider,
      Provider<SettingsRepository> settingsRepositoryProvider,
      Provider<GlobalErrorHandler> errorHandlerProvider) {
    return new DrivingModeViewModel_Factory(applicationProvider, musicRepositoryProvider, settingsRepositoryProvider, errorHandlerProvider);
  }

  public static DrivingModeViewModel newInstance(Application application,
      MusicRepository musicRepository, SettingsRepository settingsRepository,
      GlobalErrorHandler errorHandler) {
    return new DrivingModeViewModel(application, musicRepository, settingsRepository, errorHandler);
  }
}
