package com.example.aimusicplayer.ui.player;

import com.example.aimusicplayer.utils.AlbumArtCache;
import dagger.MembersInjector;
import dagger.internal.DaggerGenerated;
import dagger.internal.InjectedFieldSignature;
import dagger.internal.QualifierMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class PlayerFragment_MembersInjector implements MembersInjector<PlayerFragment> {
  private final Provider<AlbumArtCache> albumArtCacheProvider;

  public PlayerFragment_MembersInjector(Provider<AlbumArtCache> albumArtCacheProvider) {
    this.albumArtCacheProvider = albumArtCacheProvider;
  }

  public static MembersInjector<PlayerFragment> create(
      Provider<AlbumArtCache> albumArtCacheProvider) {
    return new PlayerFragment_MembersInjector(albumArtCacheProvider);
  }

  @Override
  public void injectMembers(PlayerFragment instance) {
    injectAlbumArtCache(instance, albumArtCacheProvider.get());
  }

  @InjectedFieldSignature("com.example.aimusicplayer.ui.player.PlayerFragment.albumArtCache")
  public static void injectAlbumArtCache(PlayerFragment instance, AlbumArtCache albumArtCache) {
    instance.albumArtCache = albumArtCache;
  }
}
