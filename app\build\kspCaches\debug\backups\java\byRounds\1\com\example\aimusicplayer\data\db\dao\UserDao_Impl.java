package com.example.aimusicplayer.data.db.dao;

import android.database.Cursor;
import android.os.CancellationSignal;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.room.CoroutinesRoom;
import androidx.room.EntityDeletionOrUpdateAdapter;
import androidx.room.EntityInsertionAdapter;
import androidx.room.RoomDatabase;
import androidx.room.RoomSQLiteQuery;
import androidx.room.SharedSQLiteStatement;
import androidx.room.util.CursorUtil;
import androidx.room.util.DBUtil;
import androidx.sqlite.db.SupportSQLiteStatement;
import com.example.aimusicplayer.data.db.entity.UserEntity;
import java.lang.Class;
import java.lang.Exception;
import java.lang.Object;
import java.lang.Override;
import java.lang.String;
import java.lang.SuppressWarnings;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.Callable;
import javax.annotation.processing.Generated;
import kotlin.Unit;
import kotlin.coroutines.Continuation;
import kotlinx.coroutines.flow.Flow;

@Generated("androidx.room.RoomProcessor")
@SuppressWarnings({"unchecked", "deprecation"})
public final class UserDao_Impl implements UserDao {
  private final RoomDatabase __db;

  private final EntityInsertionAdapter<UserEntity> __insertionAdapterOfUserEntity;

  private final EntityDeletionOrUpdateAdapter<UserEntity> __deletionAdapterOfUserEntity;

  private final EntityDeletionOrUpdateAdapter<UserEntity> __updateAdapterOfUserEntity;

  private final SharedSQLiteStatement __preparedStmtOfDeleteAll;

  private final SharedSQLiteStatement __preparedStmtOfUpdateUserCookie;

  public UserDao_Impl(@NonNull final RoomDatabase __db) {
    this.__db = __db;
    this.__insertionAdapterOfUserEntity = new EntityInsertionAdapter<UserEntity>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "INSERT OR REPLACE INTO `users` (`user_id`,`username`,`nickname`,`avatar_url`,`background_url`,`signature`,`follows`,`followers`,`level`,`vip_type`,`last_login_time`,`cookie`) VALUES (?,?,?,?,?,?,?,?,?,?,?,?)";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement,
          @NonNull final UserEntity entity) {
        statement.bindLong(1, entity.getUserId());
        statement.bindString(2, entity.getUsername());
        statement.bindString(3, entity.getNickname());
        statement.bindString(4, entity.getAvatarUrl());
        statement.bindString(5, entity.getBackgroundUrl());
        statement.bindString(6, entity.getSignature());
        statement.bindLong(7, entity.getFollows());
        statement.bindLong(8, entity.getFollowers());
        statement.bindLong(9, entity.getLevel());
        statement.bindLong(10, entity.getVipType());
        statement.bindLong(11, entity.getLastLoginTime());
        statement.bindString(12, entity.getCookie());
      }
    };
    this.__deletionAdapterOfUserEntity = new EntityDeletionOrUpdateAdapter<UserEntity>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "DELETE FROM `users` WHERE `user_id` = ?";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement,
          @NonNull final UserEntity entity) {
        statement.bindLong(1, entity.getUserId());
      }
    };
    this.__updateAdapterOfUserEntity = new EntityDeletionOrUpdateAdapter<UserEntity>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "UPDATE OR ABORT `users` SET `user_id` = ?,`username` = ?,`nickname` = ?,`avatar_url` = ?,`background_url` = ?,`signature` = ?,`follows` = ?,`followers` = ?,`level` = ?,`vip_type` = ?,`last_login_time` = ?,`cookie` = ? WHERE `user_id` = ?";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement,
          @NonNull final UserEntity entity) {
        statement.bindLong(1, entity.getUserId());
        statement.bindString(2, entity.getUsername());
        statement.bindString(3, entity.getNickname());
        statement.bindString(4, entity.getAvatarUrl());
        statement.bindString(5, entity.getBackgroundUrl());
        statement.bindString(6, entity.getSignature());
        statement.bindLong(7, entity.getFollows());
        statement.bindLong(8, entity.getFollowers());
        statement.bindLong(9, entity.getLevel());
        statement.bindLong(10, entity.getVipType());
        statement.bindLong(11, entity.getLastLoginTime());
        statement.bindString(12, entity.getCookie());
        statement.bindLong(13, entity.getUserId());
      }
    };
    this.__preparedStmtOfDeleteAll = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "DELETE FROM users";
        return _query;
      }
    };
    this.__preparedStmtOfUpdateUserCookie = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "UPDATE users SET cookie = ?, last_login_time = ? WHERE user_id = ?";
        return _query;
      }
    };
  }

  @Override
  public Object insert(final UserEntity user, final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        __db.beginTransaction();
        try {
          __insertionAdapterOfUserEntity.insert(user);
          __db.setTransactionSuccessful();
          return Unit.INSTANCE;
        } finally {
          __db.endTransaction();
        }
      }
    }, $completion);
  }

  @Override
  public Object delete(final UserEntity user, final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        __db.beginTransaction();
        try {
          __deletionAdapterOfUserEntity.handle(user);
          __db.setTransactionSuccessful();
          return Unit.INSTANCE;
        } finally {
          __db.endTransaction();
        }
      }
    }, $completion);
  }

  @Override
  public Object update(final UserEntity user, final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        __db.beginTransaction();
        try {
          __updateAdapterOfUserEntity.handle(user);
          __db.setTransactionSuccessful();
          return Unit.INSTANCE;
        } finally {
          __db.endTransaction();
        }
      }
    }, $completion);
  }

  @Override
  public Object deleteAll(final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        final SupportSQLiteStatement _stmt = __preparedStmtOfDeleteAll.acquire();
        try {
          __db.beginTransaction();
          try {
            _stmt.executeUpdateDelete();
            __db.setTransactionSuccessful();
            return Unit.INSTANCE;
          } finally {
            __db.endTransaction();
          }
        } finally {
          __preparedStmtOfDeleteAll.release(_stmt);
        }
      }
    }, $completion);
  }

  @Override
  public Object updateUserCookie(final long userId, final String cookie, final long lastLoginTime,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        final SupportSQLiteStatement _stmt = __preparedStmtOfUpdateUserCookie.acquire();
        int _argIndex = 1;
        _stmt.bindString(_argIndex, cookie);
        _argIndex = 2;
        _stmt.bindLong(_argIndex, lastLoginTime);
        _argIndex = 3;
        _stmt.bindLong(_argIndex, userId);
        try {
          __db.beginTransaction();
          try {
            _stmt.executeUpdateDelete();
            __db.setTransactionSuccessful();
            return Unit.INSTANCE;
          } finally {
            __db.endTransaction();
          }
        } finally {
          __preparedStmtOfUpdateUserCookie.release(_stmt);
        }
      }
    }, $completion);
  }

  @Override
  public Object getUserById(final long userId, final Continuation<? super UserEntity> $completion) {
    final String _sql = "SELECT * FROM users WHERE user_id = ?";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    _statement.bindLong(_argIndex, userId);
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<UserEntity>() {
      @Override
      @Nullable
      public UserEntity call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfUserId = CursorUtil.getColumnIndexOrThrow(_cursor, "user_id");
          final int _cursorIndexOfUsername = CursorUtil.getColumnIndexOrThrow(_cursor, "username");
          final int _cursorIndexOfNickname = CursorUtil.getColumnIndexOrThrow(_cursor, "nickname");
          final int _cursorIndexOfAvatarUrl = CursorUtil.getColumnIndexOrThrow(_cursor, "avatar_url");
          final int _cursorIndexOfBackgroundUrl = CursorUtil.getColumnIndexOrThrow(_cursor, "background_url");
          final int _cursorIndexOfSignature = CursorUtil.getColumnIndexOrThrow(_cursor, "signature");
          final int _cursorIndexOfFollows = CursorUtil.getColumnIndexOrThrow(_cursor, "follows");
          final int _cursorIndexOfFollowers = CursorUtil.getColumnIndexOrThrow(_cursor, "followers");
          final int _cursorIndexOfLevel = CursorUtil.getColumnIndexOrThrow(_cursor, "level");
          final int _cursorIndexOfVipType = CursorUtil.getColumnIndexOrThrow(_cursor, "vip_type");
          final int _cursorIndexOfLastLoginTime = CursorUtil.getColumnIndexOrThrow(_cursor, "last_login_time");
          final int _cursorIndexOfCookie = CursorUtil.getColumnIndexOrThrow(_cursor, "cookie");
          final UserEntity _result;
          if (_cursor.moveToFirst()) {
            final long _tmpUserId;
            _tmpUserId = _cursor.getLong(_cursorIndexOfUserId);
            final String _tmpUsername;
            _tmpUsername = _cursor.getString(_cursorIndexOfUsername);
            final String _tmpNickname;
            _tmpNickname = _cursor.getString(_cursorIndexOfNickname);
            final String _tmpAvatarUrl;
            _tmpAvatarUrl = _cursor.getString(_cursorIndexOfAvatarUrl);
            final String _tmpBackgroundUrl;
            _tmpBackgroundUrl = _cursor.getString(_cursorIndexOfBackgroundUrl);
            final String _tmpSignature;
            _tmpSignature = _cursor.getString(_cursorIndexOfSignature);
            final int _tmpFollows;
            _tmpFollows = _cursor.getInt(_cursorIndexOfFollows);
            final int _tmpFollowers;
            _tmpFollowers = _cursor.getInt(_cursorIndexOfFollowers);
            final int _tmpLevel;
            _tmpLevel = _cursor.getInt(_cursorIndexOfLevel);
            final int _tmpVipType;
            _tmpVipType = _cursor.getInt(_cursorIndexOfVipType);
            final long _tmpLastLoginTime;
            _tmpLastLoginTime = _cursor.getLong(_cursorIndexOfLastLoginTime);
            final String _tmpCookie;
            _tmpCookie = _cursor.getString(_cursorIndexOfCookie);
            _result = new UserEntity(_tmpUserId,_tmpUsername,_tmpNickname,_tmpAvatarUrl,_tmpBackgroundUrl,_tmpSignature,_tmpFollows,_tmpFollowers,_tmpLevel,_tmpVipType,_tmpLastLoginTime,_tmpCookie);
          } else {
            _result = null;
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @Override
  public Flow<UserEntity> getCurrentUser() {
    final String _sql = "SELECT * FROM users ORDER BY last_login_time DESC LIMIT 1";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    return CoroutinesRoom.createFlow(__db, false, new String[] {"users"}, new Callable<UserEntity>() {
      @Override
      @Nullable
      public UserEntity call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfUserId = CursorUtil.getColumnIndexOrThrow(_cursor, "user_id");
          final int _cursorIndexOfUsername = CursorUtil.getColumnIndexOrThrow(_cursor, "username");
          final int _cursorIndexOfNickname = CursorUtil.getColumnIndexOrThrow(_cursor, "nickname");
          final int _cursorIndexOfAvatarUrl = CursorUtil.getColumnIndexOrThrow(_cursor, "avatar_url");
          final int _cursorIndexOfBackgroundUrl = CursorUtil.getColumnIndexOrThrow(_cursor, "background_url");
          final int _cursorIndexOfSignature = CursorUtil.getColumnIndexOrThrow(_cursor, "signature");
          final int _cursorIndexOfFollows = CursorUtil.getColumnIndexOrThrow(_cursor, "follows");
          final int _cursorIndexOfFollowers = CursorUtil.getColumnIndexOrThrow(_cursor, "followers");
          final int _cursorIndexOfLevel = CursorUtil.getColumnIndexOrThrow(_cursor, "level");
          final int _cursorIndexOfVipType = CursorUtil.getColumnIndexOrThrow(_cursor, "vip_type");
          final int _cursorIndexOfLastLoginTime = CursorUtil.getColumnIndexOrThrow(_cursor, "last_login_time");
          final int _cursorIndexOfCookie = CursorUtil.getColumnIndexOrThrow(_cursor, "cookie");
          final UserEntity _result;
          if (_cursor.moveToFirst()) {
            final long _tmpUserId;
            _tmpUserId = _cursor.getLong(_cursorIndexOfUserId);
            final String _tmpUsername;
            _tmpUsername = _cursor.getString(_cursorIndexOfUsername);
            final String _tmpNickname;
            _tmpNickname = _cursor.getString(_cursorIndexOfNickname);
            final String _tmpAvatarUrl;
            _tmpAvatarUrl = _cursor.getString(_cursorIndexOfAvatarUrl);
            final String _tmpBackgroundUrl;
            _tmpBackgroundUrl = _cursor.getString(_cursorIndexOfBackgroundUrl);
            final String _tmpSignature;
            _tmpSignature = _cursor.getString(_cursorIndexOfSignature);
            final int _tmpFollows;
            _tmpFollows = _cursor.getInt(_cursorIndexOfFollows);
            final int _tmpFollowers;
            _tmpFollowers = _cursor.getInt(_cursorIndexOfFollowers);
            final int _tmpLevel;
            _tmpLevel = _cursor.getInt(_cursorIndexOfLevel);
            final int _tmpVipType;
            _tmpVipType = _cursor.getInt(_cursorIndexOfVipType);
            final long _tmpLastLoginTime;
            _tmpLastLoginTime = _cursor.getLong(_cursorIndexOfLastLoginTime);
            final String _tmpCookie;
            _tmpCookie = _cursor.getString(_cursorIndexOfCookie);
            _result = new UserEntity(_tmpUserId,_tmpUsername,_tmpNickname,_tmpAvatarUrl,_tmpBackgroundUrl,_tmpSignature,_tmpFollows,_tmpFollowers,_tmpLevel,_tmpVipType,_tmpLastLoginTime,_tmpCookie);
          } else {
            _result = null;
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public Flow<List<UserEntity>> getAllUsers() {
    final String _sql = "SELECT * FROM users";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    return CoroutinesRoom.createFlow(__db, false, new String[] {"users"}, new Callable<List<UserEntity>>() {
      @Override
      @NonNull
      public List<UserEntity> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfUserId = CursorUtil.getColumnIndexOrThrow(_cursor, "user_id");
          final int _cursorIndexOfUsername = CursorUtil.getColumnIndexOrThrow(_cursor, "username");
          final int _cursorIndexOfNickname = CursorUtil.getColumnIndexOrThrow(_cursor, "nickname");
          final int _cursorIndexOfAvatarUrl = CursorUtil.getColumnIndexOrThrow(_cursor, "avatar_url");
          final int _cursorIndexOfBackgroundUrl = CursorUtil.getColumnIndexOrThrow(_cursor, "background_url");
          final int _cursorIndexOfSignature = CursorUtil.getColumnIndexOrThrow(_cursor, "signature");
          final int _cursorIndexOfFollows = CursorUtil.getColumnIndexOrThrow(_cursor, "follows");
          final int _cursorIndexOfFollowers = CursorUtil.getColumnIndexOrThrow(_cursor, "followers");
          final int _cursorIndexOfLevel = CursorUtil.getColumnIndexOrThrow(_cursor, "level");
          final int _cursorIndexOfVipType = CursorUtil.getColumnIndexOrThrow(_cursor, "vip_type");
          final int _cursorIndexOfLastLoginTime = CursorUtil.getColumnIndexOrThrow(_cursor, "last_login_time");
          final int _cursorIndexOfCookie = CursorUtil.getColumnIndexOrThrow(_cursor, "cookie");
          final List<UserEntity> _result = new ArrayList<UserEntity>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final UserEntity _item;
            final long _tmpUserId;
            _tmpUserId = _cursor.getLong(_cursorIndexOfUserId);
            final String _tmpUsername;
            _tmpUsername = _cursor.getString(_cursorIndexOfUsername);
            final String _tmpNickname;
            _tmpNickname = _cursor.getString(_cursorIndexOfNickname);
            final String _tmpAvatarUrl;
            _tmpAvatarUrl = _cursor.getString(_cursorIndexOfAvatarUrl);
            final String _tmpBackgroundUrl;
            _tmpBackgroundUrl = _cursor.getString(_cursorIndexOfBackgroundUrl);
            final String _tmpSignature;
            _tmpSignature = _cursor.getString(_cursorIndexOfSignature);
            final int _tmpFollows;
            _tmpFollows = _cursor.getInt(_cursorIndexOfFollows);
            final int _tmpFollowers;
            _tmpFollowers = _cursor.getInt(_cursorIndexOfFollowers);
            final int _tmpLevel;
            _tmpLevel = _cursor.getInt(_cursorIndexOfLevel);
            final int _tmpVipType;
            _tmpVipType = _cursor.getInt(_cursorIndexOfVipType);
            final long _tmpLastLoginTime;
            _tmpLastLoginTime = _cursor.getLong(_cursorIndexOfLastLoginTime);
            final String _tmpCookie;
            _tmpCookie = _cursor.getString(_cursorIndexOfCookie);
            _item = new UserEntity(_tmpUserId,_tmpUsername,_tmpNickname,_tmpAvatarUrl,_tmpBackgroundUrl,_tmpSignature,_tmpFollows,_tmpFollowers,_tmpLevel,_tmpVipType,_tmpLastLoginTime,_tmpCookie);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @NonNull
  public static List<Class<?>> getRequiredConverters() {
    return Collections.emptyList();
  }
}
