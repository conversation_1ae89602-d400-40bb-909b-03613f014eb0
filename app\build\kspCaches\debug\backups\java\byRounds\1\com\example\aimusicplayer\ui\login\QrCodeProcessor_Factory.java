package com.example.aimusicplayer.ui.login;

import android.content.Context;
import com.example.aimusicplayer.data.repository.UserRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class QrCodeProcessor_Factory implements Factory<QrCodeProcessor> {
  private final Provider<Context> contextProvider;

  private final Provider<UserRepository> userRepositoryProvider;

  public QrCodeProcessor_Factory(Provider<Context> contextProvider,
      Provider<UserRepository> userRepositoryProvider) {
    this.contextProvider = contextProvider;
    this.userRepositoryProvider = userRepositoryProvider;
  }

  @Override
  public QrCodeProcessor get() {
    return newInstance(contextProvider.get(), userRepositoryProvider.get());
  }

  public static QrCodeProcessor_Factory create(Provider<Context> contextProvider,
      Provider<UserRepository> userRepositoryProvider) {
    return new QrCodeProcessor_Factory(contextProvider, userRepositoryProvider);
  }

  public static QrCodeProcessor newInstance(Context context, UserRepository userRepository) {
    return new QrCodeProcessor(context, userRepository);
  }
}
