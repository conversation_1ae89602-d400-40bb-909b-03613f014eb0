package com.example.aimusicplayer.utils

/**
 * 常量类
 */
object Constants {
    // API相关
    const val BASE_URL = "https://1355831898-4499wupl9z.ap-guangzhou.tencentscf.com"

    // SharedPreferences
    const val PREF_NAME = "music_player_pref"
    const val PREF_PLAY_MODE = "play_mode"
    const val PREF_NIGHT_MODE = "night_mode"
    const val PREF_AUTO_PLAY = "auto_play"
    const val PREF_AUTO_VOICE_IN_DRIVING = "auto_voice_in_driving"
    const val PREF_COOKIE = "user_cookie"
    const val PREF_CURRENT_SONG_ID = "current_song_id"
    const val PREF_USER_ID = "user_id"
    const val PREF_USER_NAME = "user_name"
    const val PREF_USER_AVATAR = "user_avatar"
    const val PREF_IS_LOGGED_IN = "is_logged_in"
    const val PREF_IS_GUEST = "is_guest"

    // 播放模式
    const val PLAY_MODE_LOOP = 0
    const val PLAY_MODE_SHUFFLE = 1
    const val PLAY_MODE_SINGLE = 2
    const val PLAY_MODE_SEQUENCE = 3

    // 播放器相关
    const val ACTION_PLAY = "com.example.aimusicplayer.ACTION_PLAY"
    const val ACTION_PAUSE = "com.example.aimusicplayer.ACTION_PAUSE"
    const val ACTION_PREVIOUS = "com.example.aimusicplayer.ACTION_PREVIOUS"
    const val ACTION_NEXT = "com.example.aimusicplayer.ACTION_NEXT"
    const val ACTION_STOP = "com.example.aimusicplayer.ACTION_STOP"
    const val ACTION_TOGGLE_PLAY_MODE = "com.example.aimusicplayer.ACTION_TOGGLE_PLAY_MODE"
    const val ACTION_TOGGLE_FAVORITE = "com.example.aimusicplayer.ACTION_TOGGLE_FAVORITE"

    // 通知渠道
    const val NOTIFICATION_CHANNEL_ID = "music_playback_channel"
    const val NOTIFICATION_ID = 1

    // 缓存相关
    const val CACHE_SIZE_MB = 50 // 50MB
    const val CACHE_EXPIRATION_DAYS = 7 // 7天

    // 权限请求码
    const val PERMISSION_REQUEST_CODE = 100

    // 数据库相关
    const val DATABASE_NAME = "music_player_db"
    const val DATABASE_VERSION = 1

    // 播放历史相关
    const val MAX_PLAY_HISTORY_ITEMS = 100

    // 心动模式相关
    const val HEARTBEAT_MODE_SONGS_COUNT = 20
}
