package com.example.aimusicplayer.utils

import android.content.Context
import android.graphics.Bitmap
import android.graphics.BitmapFactory
import android.net.Uri
import android.util.Log
import android.util.LruCache
import androidx.core.graphics.drawable.toBitmap
import com.bumptech.glide.Glide
import com.bumptech.glide.load.engine.DiskCacheStrategy
import com.bumptech.glide.request.RequestOptions
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import java.io.File
import java.io.FileOutputStream
import java.io.IOException
import java.util.concurrent.ConcurrentHashMap
import java.util.concurrent.TimeUnit

/**
 * 增强型图片缓存管理器
 * 提供内存缓存和磁盘缓存，支持异步加载和缓存过期
 */
class EnhancedImageCache(private val context: Context) {

    companion object {
        private const val TAG = "EnhancedImageCache"
        private const val CACHE_DIR_NAME = "enhanced_image_cache"
        private const val DEFAULT_MEMORY_CACHE_SIZE = 1024 * 1024 * 20 // 20MB
        private const val DEFAULT_DISK_CACHE_SIZE = 1024 * 1024 * 100 // 100MB
        private const val DEFAULT_CACHE_EXPIRATION = 7 * 24 * 60 * 60 * 1000L // 7天过期 (毫秒)

        // 单例实例
        @Volatile
        private var INSTANCE: EnhancedImageCache? = null

        fun getInstance(context: Context): EnhancedImageCache {
            return INSTANCE ?: synchronized(this) {
                INSTANCE ?: EnhancedImageCache(context.applicationContext).also { INSTANCE = it }
            }
        }
    }

    // 内存缓存
    private val memoryCache: LruCache<String, Bitmap>

    // 缓存过期时间映射
    private val cacheExpirationMap = ConcurrentHashMap<String, Long>()

    // 缓存目录
    private val cacheDir: File

    // 当前缓存大小
    private var currentDiskCacheSize = 0L

    init {
        // 初始化内存缓存
        val maxMemory = (Runtime.getRuntime().maxMemory() / 1024).toInt()
        val cacheSize = maxMemory / 8 // 使用1/8的可用内存作为缓存

        memoryCache = object : LruCache<String, Bitmap>(cacheSize) {
            override fun sizeOf(key: String, bitmap: Bitmap): Int {
                // 返回图片大小（KB）
                return bitmap.byteCount / 1024
            }
        }

        // 初始化缓存目录
        cacheDir = File(context.cacheDir, CACHE_DIR_NAME)
        if (!cacheDir.exists()) {
            cacheDir.mkdirs()
        }

        // 计算当前缓存大小
        calculateDiskCacheSize()

        // 清理过期缓存（在初始化时不使用协程）
        cleanExpiredCacheSync()
    }

    /**
     * 计算当前磁盘缓存大小
     */
    private fun calculateDiskCacheSize() {
        currentDiskCacheSize = 0L
        cacheDir.listFiles()?.forEach { file ->
            currentDiskCacheSize += file.length()
        }
    }

    /**
     * 清理过期缓存（同步版本，用于初始化）
     */
    private fun cleanExpiredCacheSync() {
        try {
            val currentTime = System.currentTimeMillis()
            val expiredKeys = mutableListOf<String>()

            // 查找过期的缓存项
            cacheExpirationMap.forEach { (key, expirationTime) ->
                if (currentTime > expirationTime) {
                    expiredKeys.add(key)
                }
            }

            // 删除过期的缓存项
            expiredKeys.forEach { key ->
                removeFromCache(key)
            }

            // 检查磁盘缓存
            cacheDir.listFiles()?.forEach { file ->
                val lastModified = file.lastModified()
                if (currentTime - lastModified > DEFAULT_CACHE_EXPIRATION) {
                    file.delete()
                }
            }

            // 重新计算缓存大小
            calculateDiskCacheSize()

            // 如果缓存大小超过限制，删除最旧的文件
            if (currentDiskCacheSize > DEFAULT_DISK_CACHE_SIZE) {
                val files = cacheDir.listFiles()?.sortedBy { it.lastModified() } ?: emptyList()
                for (file in files) {
                    file.delete()
                    currentDiskCacheSize -= file.length()
                    if (currentDiskCacheSize <= DEFAULT_DISK_CACHE_SIZE) {
                        break
                    }
                }
            }

            Log.d(TAG, "清理过期缓存完成，当前缓存大小: ${currentDiskCacheSize / 1024 / 1024}MB")
        } catch (e: Exception) {
            Log.e(TAG, "清理过期缓存失败", e)
        }
    }

    /**
     * 清理过期缓存（协程版本，用于后台清理）
     */
    suspend fun cleanExpiredCache() = withContext(Dispatchers.IO) {
        cleanExpiredCacheSync()
    }

    /**
     * 从缓存中移除指定键的项
     */
    private fun removeFromCache(key: String) {
        // 从内存缓存中移除
        memoryCache.remove(key)

        // 从过期映射中移除
        cacheExpirationMap.remove(key)

        // 从磁盘缓存中移除
        val file = File(cacheDir, key)
        if (file.exists()) {
            currentDiskCacheSize -= file.length()
            file.delete()
        }
    }

    /**
     * 获取图片
     * @param uri 图片URI
     * @param key 缓存键
     * @return 位图，如果不存在则返回null
     */
    suspend fun getBitmap(uri: Uri, key: String): Bitmap? = withContext(Dispatchers.IO) {
        try {
            // 先从内存缓存获取
            var bitmap = memoryCache.get(key)

            // 检查是否过期
            val expirationTime = cacheExpirationMap[key]
            val isExpired = expirationTime != null && System.currentTimeMillis() > expirationTime

            if (bitmap != null && !isExpired) {
                // 内存缓存命中且未过期
                return@withContext bitmap
            }

            // 从磁盘缓存获取
            val file = File(cacheDir, key)
            if (file.exists() && !isExpired) {
                try {
                    bitmap = BitmapFactory.decodeFile(file.absolutePath)
                    if (bitmap != null) {
                        // 添加到内存缓存
                        memoryCache.put(key, bitmap)
                        return@withContext bitmap
                    }
                } catch (e: Exception) {
                    Log.e(TAG, "从磁盘缓存加载图片失败", e)
                }
            }

            // 使用Glide加载图片 - 优化版本
            try {
                bitmap = Glide.with(context)
                    .asBitmap()
                    .load(uri)
                    .apply(RequestOptions()
                        .diskCacheStrategy(DiskCacheStrategy.ALL) // 启用磁盘缓存
                        .centerCrop()
                        .timeout(5000) // 5秒超时
                        .skipMemoryCache(false))
                    .submit(512, 512) // 限制尺寸，提高性能
                    .get(3000, TimeUnit.MILLISECONDS) // 3秒超时

                // 异步保存到缓存，避免阻塞
                if (bitmap != null) {
                    // 立即添加到内存缓存
                    memoryCache.put(key, bitmap)
                    // 异步保存到磁盘
                    GlobalScope.launch(Dispatchers.IO) {
                        try {
                            saveBitmap(bitmap, key)
                        } catch (e: Exception) {
                            Log.e(TAG, "异步保存位图失败", e)
                        }
                    }
                }

                return@withContext bitmap
            } catch (e: Exception) {
                Log.e(TAG, "使用Glide加载图片失败", e)
                return@withContext null
            }
        } catch (e: Exception) {
            Log.e(TAG, "获取图片失败", e)
            return@withContext null
        }
    }

    /**
     * 保存位图到缓存
     * @param bitmap 位图
     * @param key 缓存键
     */
    suspend fun saveBitmap(bitmap: Bitmap, key: String) = withContext(Dispatchers.IO) {
        try {
            // 保存到内存缓存
            memoryCache.put(key, bitmap)

            // 设置过期时间
            cacheExpirationMap[key] = System.currentTimeMillis() + DEFAULT_CACHE_EXPIRATION

            // 保存到磁盘缓存
            val file = File(cacheDir, key)
            var outputStream: FileOutputStream? = null
            try {
                outputStream = FileOutputStream(file)
                bitmap.compress(Bitmap.CompressFormat.PNG, 100, outputStream)
                outputStream.flush()

                // 更新缓存大小
                currentDiskCacheSize += file.length()

                // 如果缓存大小超过限制，清理旧缓存
                if (currentDiskCacheSize > DEFAULT_DISK_CACHE_SIZE) {
                    // 在协程中已经，可以直接调用挂起函数
                    cleanExpiredCache()
                } else {
                    Log.d(TAG, "缓存大小在限制范围内，无需清理")
                }
            } catch (e: IOException) {
                Log.e(TAG, "保存图片到磁盘缓存失败", e)
            } finally {
                outputStream?.close()
            }
        } catch (e: Exception) {
            Log.e(TAG, "保存位图到缓存失败", e)
        }
    }

    /**
     * 清除所有缓存
     */
    suspend fun clearCache() = withContext(Dispatchers.IO) {
        try {
            // 清除内存缓存
            memoryCache.evictAll()

            // 清除过期映射
            cacheExpirationMap.clear()

            // 清除磁盘缓存
            cacheDir.listFiles()?.forEach { it.delete() }

            // 重置缓存大小
            currentDiskCacheSize = 0L

            Log.d(TAG, "清除所有缓存完成")
        } catch (e: Exception) {
            Log.e(TAG, "清除所有缓存失败", e)
        }
    }
}
