package com.example.aimusicplayer.data.db.dao;

import android.database.Cursor;
import android.os.CancellationSignal;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.room.CoroutinesRoom;
import androidx.room.EntityInsertionAdapter;
import androidx.room.RoomDatabase;
import androidx.room.RoomSQLiteQuery;
import androidx.room.SharedSQLiteStatement;
import androidx.room.util.CursorUtil;
import androidx.room.util.DBUtil;
import androidx.sqlite.db.SupportSQLiteStatement;
import com.example.aimusicplayer.data.db.entity.ApiCacheEntity;
import java.lang.Class;
import java.lang.Exception;
import java.lang.Integer;
import java.lang.Long;
import java.lang.Object;
import java.lang.Override;
import java.lang.String;
import java.lang.SuppressWarnings;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.Callable;
import javax.annotation.processing.Generated;
import kotlin.Unit;
import kotlin.coroutines.Continuation;

@Generated("androidx.room.RoomProcessor")
@SuppressWarnings({"unchecked", "deprecation"})
public final class ApiCacheDao_Impl implements ApiCacheDao {
  private final RoomDatabase __db;

  private final EntityInsertionAdapter<ApiCacheEntity> __insertionAdapterOfApiCacheEntity;

  private final SharedSQLiteStatement __preparedStmtOfDeleteCache;

  private final SharedSQLiteStatement __preparedStmtOfClearAll;

  private final SharedSQLiteStatement __preparedStmtOfClearExpiredCache;

  private final SharedSQLiteStatement __preparedStmtOfClearCacheByType;

  public ApiCacheDao_Impl(@NonNull final RoomDatabase __db) {
    this.__db = __db;
    this.__insertionAdapterOfApiCacheEntity = new EntityInsertionAdapter<ApiCacheEntity>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "INSERT OR REPLACE INTO `api_cache` (`cache_key`,`data`,`cache_time`,`expiration_time`,`cache_type`) VALUES (?,?,?,?,?)";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement,
          @NonNull final ApiCacheEntity entity) {
        statement.bindString(1, entity.getCacheKey());
        statement.bindString(2, entity.getData());
        statement.bindLong(3, entity.getCacheTime());
        statement.bindLong(4, entity.getExpirationTime());
        statement.bindString(5, entity.getCacheType());
      }
    };
    this.__preparedStmtOfDeleteCache = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "DELETE FROM api_cache WHERE cache_key = ?";
        return _query;
      }
    };
    this.__preparedStmtOfClearAll = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "DELETE FROM api_cache";
        return _query;
      }
    };
    this.__preparedStmtOfClearExpiredCache = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "DELETE FROM api_cache WHERE (cache_time + expiration_time) < ?";
        return _query;
      }
    };
    this.__preparedStmtOfClearCacheByType = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "DELETE FROM api_cache WHERE cache_type = ?";
        return _query;
      }
    };
  }

  @Override
  public Object insert(final ApiCacheEntity cache, final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        __db.beginTransaction();
        try {
          __insertionAdapterOfApiCacheEntity.insert(cache);
          __db.setTransactionSuccessful();
          return Unit.INSTANCE;
        } finally {
          __db.endTransaction();
        }
      }
    }, $completion);
  }

  @Override
  public Object deleteCache(final String cacheKey, final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        final SupportSQLiteStatement _stmt = __preparedStmtOfDeleteCache.acquire();
        int _argIndex = 1;
        _stmt.bindString(_argIndex, cacheKey);
        try {
          __db.beginTransaction();
          try {
            _stmt.executeUpdateDelete();
            __db.setTransactionSuccessful();
            return Unit.INSTANCE;
          } finally {
            __db.endTransaction();
          }
        } finally {
          __preparedStmtOfDeleteCache.release(_stmt);
        }
      }
    }, $completion);
  }

  @Override
  public Object clearAll(final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        final SupportSQLiteStatement _stmt = __preparedStmtOfClearAll.acquire();
        try {
          __db.beginTransaction();
          try {
            _stmt.executeUpdateDelete();
            __db.setTransactionSuccessful();
            return Unit.INSTANCE;
          } finally {
            __db.endTransaction();
          }
        } finally {
          __preparedStmtOfClearAll.release(_stmt);
        }
      }
    }, $completion);
  }

  @Override
  public Object clearExpiredCache(final long currentTime,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        final SupportSQLiteStatement _stmt = __preparedStmtOfClearExpiredCache.acquire();
        int _argIndex = 1;
        _stmt.bindLong(_argIndex, currentTime);
        try {
          __db.beginTransaction();
          try {
            _stmt.executeUpdateDelete();
            __db.setTransactionSuccessful();
            return Unit.INSTANCE;
          } finally {
            __db.endTransaction();
          }
        } finally {
          __preparedStmtOfClearExpiredCache.release(_stmt);
        }
      }
    }, $completion);
  }

  @Override
  public Object clearCacheByType(final String cacheType,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        final SupportSQLiteStatement _stmt = __preparedStmtOfClearCacheByType.acquire();
        int _argIndex = 1;
        _stmt.bindString(_argIndex, cacheType);
        try {
          __db.beginTransaction();
          try {
            _stmt.executeUpdateDelete();
            __db.setTransactionSuccessful();
            return Unit.INSTANCE;
          } finally {
            __db.endTransaction();
          }
        } finally {
          __preparedStmtOfClearCacheByType.release(_stmt);
        }
      }
    }, $completion);
  }

  @Override
  public Object getCache(final String cacheKey,
      final Continuation<? super ApiCacheEntity> $completion) {
    final String _sql = "SELECT * FROM api_cache WHERE cache_key = ?";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    _statement.bindString(_argIndex, cacheKey);
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<ApiCacheEntity>() {
      @Override
      @Nullable
      public ApiCacheEntity call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfCacheKey = CursorUtil.getColumnIndexOrThrow(_cursor, "cache_key");
          final int _cursorIndexOfData = CursorUtil.getColumnIndexOrThrow(_cursor, "data");
          final int _cursorIndexOfCacheTime = CursorUtil.getColumnIndexOrThrow(_cursor, "cache_time");
          final int _cursorIndexOfExpirationTime = CursorUtil.getColumnIndexOrThrow(_cursor, "expiration_time");
          final int _cursorIndexOfCacheType = CursorUtil.getColumnIndexOrThrow(_cursor, "cache_type");
          final ApiCacheEntity _result;
          if (_cursor.moveToFirst()) {
            final String _tmpCacheKey;
            _tmpCacheKey = _cursor.getString(_cursorIndexOfCacheKey);
            final String _tmpData;
            _tmpData = _cursor.getString(_cursorIndexOfData);
            final long _tmpCacheTime;
            _tmpCacheTime = _cursor.getLong(_cursorIndexOfCacheTime);
            final long _tmpExpirationTime;
            _tmpExpirationTime = _cursor.getLong(_cursorIndexOfExpirationTime);
            final String _tmpCacheType;
            _tmpCacheType = _cursor.getString(_cursorIndexOfCacheType);
            _result = new ApiCacheEntity(_tmpCacheKey,_tmpData,_tmpCacheTime,_tmpExpirationTime,_tmpCacheType);
          } else {
            _result = null;
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @Override
  public Object getAllCache(final Continuation<? super List<ApiCacheEntity>> $completion) {
    final String _sql = "SELECT * FROM api_cache";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<List<ApiCacheEntity>>() {
      @Override
      @NonNull
      public List<ApiCacheEntity> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfCacheKey = CursorUtil.getColumnIndexOrThrow(_cursor, "cache_key");
          final int _cursorIndexOfData = CursorUtil.getColumnIndexOrThrow(_cursor, "data");
          final int _cursorIndexOfCacheTime = CursorUtil.getColumnIndexOrThrow(_cursor, "cache_time");
          final int _cursorIndexOfExpirationTime = CursorUtil.getColumnIndexOrThrow(_cursor, "expiration_time");
          final int _cursorIndexOfCacheType = CursorUtil.getColumnIndexOrThrow(_cursor, "cache_type");
          final List<ApiCacheEntity> _result = new ArrayList<ApiCacheEntity>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final ApiCacheEntity _item;
            final String _tmpCacheKey;
            _tmpCacheKey = _cursor.getString(_cursorIndexOfCacheKey);
            final String _tmpData;
            _tmpData = _cursor.getString(_cursorIndexOfData);
            final long _tmpCacheTime;
            _tmpCacheTime = _cursor.getLong(_cursorIndexOfCacheTime);
            final long _tmpExpirationTime;
            _tmpExpirationTime = _cursor.getLong(_cursorIndexOfExpirationTime);
            final String _tmpCacheType;
            _tmpCacheType = _cursor.getString(_cursorIndexOfCacheType);
            _item = new ApiCacheEntity(_tmpCacheKey,_tmpData,_tmpCacheTime,_tmpExpirationTime,_tmpCacheType);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @Override
  public Object getCacheCount(final Continuation<? super Integer> $completion) {
    final String _sql = "SELECT COUNT(*) FROM api_cache";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<Integer>() {
      @Override
      @NonNull
      public Integer call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final Integer _result;
          if (_cursor.moveToFirst()) {
            final int _tmp;
            _tmp = _cursor.getInt(0);
            _result = _tmp;
          } else {
            _result = 0;
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @Override
  public Object getCacheSize(final Continuation<? super Long> $completion) {
    final String _sql = "SELECT SUM(LENGTH(data)) FROM api_cache";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<Long>() {
      @Override
      @Nullable
      public Long call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final Long _result;
          if (_cursor.moveToFirst()) {
            final Long _tmp;
            if (_cursor.isNull(0)) {
              _tmp = null;
            } else {
              _tmp = _cursor.getLong(0);
            }
            _result = _tmp;
          } else {
            _result = null;
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @NonNull
  public static List<Class<?>> getRequiredConverters() {
    return Collections.emptyList();
  }
}
