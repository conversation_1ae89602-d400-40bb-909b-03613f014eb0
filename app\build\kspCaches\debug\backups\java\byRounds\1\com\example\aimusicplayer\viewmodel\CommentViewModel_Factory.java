package com.example.aimusicplayer.viewmodel;

import android.app.Application;
import com.example.aimusicplayer.data.repository.CommentRepository;
import com.example.aimusicplayer.data.repository.MusicRepository;
import com.example.aimusicplayer.error.GlobalErrorHandler;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class CommentViewModel_Factory implements Factory<CommentViewModel> {
  private final Provider<Application> applicationProvider;

  private final Provider<MusicRepository> musicRepositoryProvider;

  private final Provider<CommentRepository> commentRepositoryProvider;

  private final Provider<GlobalErrorHandler> errorHandlerProvider;

  public CommentViewModel_Factory(Provider<Application> applicationProvider,
      Provider<MusicRepository> musicRepositoryProvider,
      Provider<CommentRepository> commentRepositoryProvider,
      Provider<GlobalErrorHandler> errorHandlerProvider) {
    this.applicationProvider = applicationProvider;
    this.musicRepositoryProvider = musicRepositoryProvider;
    this.commentRepositoryProvider = commentRepositoryProvider;
    this.errorHandlerProvider = errorHandlerProvider;
  }

  @Override
  public CommentViewModel get() {
    return newInstance(applicationProvider.get(), musicRepositoryProvider.get(), commentRepositoryProvider.get(), errorHandlerProvider.get());
  }

  public static CommentViewModel_Factory create(Provider<Application> applicationProvider,
      Provider<MusicRepository> musicRepositoryProvider,
      Provider<CommentRepository> commentRepositoryProvider,
      Provider<GlobalErrorHandler> errorHandlerProvider) {
    return new CommentViewModel_Factory(applicationProvider, musicRepositoryProvider, commentRepositoryProvider, errorHandlerProvider);
  }

  public static CommentViewModel newInstance(Application application,
      MusicRepository musicRepository, CommentRepository commentRepository,
      GlobalErrorHandler errorHandler) {
    return new CommentViewModel(application, musicRepository, commentRepository, errorHandler);
  }
}
