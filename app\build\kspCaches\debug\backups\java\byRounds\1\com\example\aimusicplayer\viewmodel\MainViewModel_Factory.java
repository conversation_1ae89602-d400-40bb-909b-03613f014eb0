package com.example.aimusicplayer.viewmodel;

import android.app.Application;
import com.example.aimusicplayer.api.ApiManager;
import com.example.aimusicplayer.api.UnifiedApiService;
import com.example.aimusicplayer.data.repository.UserRepository;
import com.example.aimusicplayer.error.GlobalErrorHandler;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class MainViewModel_Factory implements Factory<MainViewModel> {
  private final Provider<Application> applicationProvider;

  private final Provider<UserRepository> userRepositoryProvider;

  private final Provider<UnifiedApiService> apiServiceProvider;

  private final Provider<ApiManager> apiManagerProvider;

  private final Provider<GlobalErrorHandler> errorHandlerProvider;

  public MainViewModel_Factory(Provider<Application> applicationProvider,
      Provider<UserRepository> userRepositoryProvider,
      Provider<UnifiedApiService> apiServiceProvider, Provider<ApiManager> apiManagerProvider,
      Provider<GlobalErrorHandler> errorHandlerProvider) {
    this.applicationProvider = applicationProvider;
    this.userRepositoryProvider = userRepositoryProvider;
    this.apiServiceProvider = apiServiceProvider;
    this.apiManagerProvider = apiManagerProvider;
    this.errorHandlerProvider = errorHandlerProvider;
  }

  @Override
  public MainViewModel get() {
    return newInstance(applicationProvider.get(), userRepositoryProvider.get(), apiServiceProvider.get(), apiManagerProvider.get(), errorHandlerProvider.get());
  }

  public static MainViewModel_Factory create(Provider<Application> applicationProvider,
      Provider<UserRepository> userRepositoryProvider,
      Provider<UnifiedApiService> apiServiceProvider, Provider<ApiManager> apiManagerProvider,
      Provider<GlobalErrorHandler> errorHandlerProvider) {
    return new MainViewModel_Factory(applicationProvider, userRepositoryProvider, apiServiceProvider, apiManagerProvider, errorHandlerProvider);
  }

  public static MainViewModel newInstance(Application application, UserRepository userRepository,
      UnifiedApiService apiService, ApiManager apiManager, GlobalErrorHandler errorHandler) {
    return new MainViewModel(application, userRepository, apiService, apiManager, errorHandler);
  }
}
