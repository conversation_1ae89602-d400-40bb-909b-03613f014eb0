package com.example.aimusicplayer.di;

import android.content.Context;
import com.example.aimusicplayer.utils.AlbumArtCache;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata("dagger.hilt.android.qualifiers.ApplicationContext")
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class AppModule_ProvideAlbumArtCacheFactory implements Factory<AlbumArtCache> {
  private final Provider<Context> contextProvider;

  public AppModule_ProvideAlbumArtCacheFactory(Provider<Context> contextProvider) {
    this.contextProvider = contextProvider;
  }

  @Override
  public AlbumArtCache get() {
    return provideAlbumArtCache(contextProvider.get());
  }

  public static AppModule_ProvideAlbumArtCacheFactory create(Provider<Context> contextProvider) {
    return new AppModule_ProvideAlbumArtCacheFactory(contextProvider);
  }

  public static AlbumArtCache provideAlbumArtCache(Context context) {
    return Preconditions.checkNotNullFromProvides(AppModule.INSTANCE.provideAlbumArtCache(context));
  }
}
