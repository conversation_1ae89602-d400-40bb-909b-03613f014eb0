package com.example.aimusicplayer.service;

import com.example.aimusicplayer.data.source.MusicDataSource;
import dagger.MembersInjector;
import dagger.internal.DaggerGenerated;
import dagger.internal.InjectedFieldSignature;
import dagger.internal.QualifierMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class UnifiedPlaybackService_MembersInjector implements MembersInjector<UnifiedPlaybackService> {
  private final Provider<MusicDataSource> musicDataSourceProvider;

  public UnifiedPlaybackService_MembersInjector(Provider<MusicDataSource> musicDataSourceProvider) {
    this.musicDataSourceProvider = musicDataSourceProvider;
  }

  public static MembersInjector<UnifiedPlaybackService> create(
      Provider<MusicDataSource> musicDataSourceProvider) {
    return new UnifiedPlaybackService_MembersInjector(musicDataSourceProvider);
  }

  @Override
  public void injectMembers(UnifiedPlaybackService instance) {
    injectMusicDataSource(instance, musicDataSourceProvider.get());
  }

  @InjectedFieldSignature("com.example.aimusicplayer.service.UnifiedPlaybackService.musicDataSource")
  public static void injectMusicDataSource(UnifiedPlaybackService instance,
      MusicDataSource musicDataSource) {
    instance.musicDataSource = musicDataSource;
  }
}
