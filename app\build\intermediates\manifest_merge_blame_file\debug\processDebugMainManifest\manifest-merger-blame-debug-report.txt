1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.example.aimusicplayer"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="24"
9        android:targetSdkVersion="34" />
10
11    <!-- 百度语音SDK所需权限 -->
12    <uses-permission android:name="android.permission.INTERNET" />
12-->C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:6:5-66
12-->C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:6:22-64
13    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
13-->C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:7:5-78
13-->C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:7:22-76
14    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
14-->C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:8:5-75
14-->C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:8:22-73
15    <uses-permission android:name="android.permission.MODIFY_AUDIO_SETTINGS" />
15-->C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:9:5-79
15-->C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:9:22-77
16
17    <!-- Android 13 (API 33) 及以上版本需要区分的存储权限 -->
18    <uses-permission
18-->C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:12:5-13:38
19        android:name="android.permission.WRITE_EXTERNAL_STORAGE"
19-->C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:12:22-78
20        android:maxSdkVersion="32" />
20-->C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:13:9-35
21    <uses-permission
21-->C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:14:5-15:38
22        android:name="android.permission.READ_EXTERNAL_STORAGE"
22-->C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:14:22-77
23        android:maxSdkVersion="32" />
23-->C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:15:9-35
24
25    <!-- Android 13 新增的精细存储权限 -->
26    <uses-permission android:name="android.permission.READ_MEDIA_AUDIO" />
26-->C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:18:5-75
26-->C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:18:22-72
27    <uses-permission android:name="android.permission.READ_MEDIA_IMAGES" />
27-->C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:19:5-76
27-->C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:19:22-73
28    <uses-permission android:name="android.permission.READ_MEDIA_VIDEO" />
28-->C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:20:5-75
28-->C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:20:22-72
29
30    <!-- 应用所需的其他权限 -->
31    <uses-permission android:name="android.permission.RECORD_AUDIO" />
31-->C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:23:5-70
31-->C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:23:22-68
32    <uses-permission android:name="android.permission.CHANGE_NETWORK_STATE" />
32-->C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:24:5-78
32-->C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:24:22-76
33    <uses-permission android:name="android.permission.READ_PHONE_STATE" />
33-->C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:25:5-74
33-->C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:25:22-72
34    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
34-->C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:26:5-78
34-->C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:26:22-75
35
36    <!-- 通知权限 (Android 13+) -->
37    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
37-->C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:29:5-77
37-->C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:29:22-74
38
39    <!-- 前台服务权限 -->
40    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
40-->C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:32:5-77
40-->C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:32:22-74
41
42    <!-- 震动权限 -->
43    <uses-permission android:name="android.permission.VIBRATE" />
43-->C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:35:5-66
43-->C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:35:22-63
44
45    <!-- 唤醒锁权限 (ExoPlayer播放时保持设备唤醒) -->
46    <uses-permission android:name="android.permission.WAKE_LOCK" />
46-->C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:38:5-68
46-->C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:38:22-65
47    <uses-permission android:name="android.permission.CAMERA" /> <!-- Don't require camera, as this requires a rear camera. This allows it to work on the Nexus 7 -->
47-->[com.journeyapps:zxing-android-embedded:4.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8c8cb4a267669986cd76225679b8b78b\transformed\zxing-android-embedded-4.2.0\AndroidManifest.xml:22:5-65
47-->[com.journeyapps:zxing-android-embedded:4.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8c8cb4a267669986cd76225679b8b78b\transformed\zxing-android-embedded-4.2.0\AndroidManifest.xml:22:22-62
48    <uses-feature
48-->[com.journeyapps:zxing-android-embedded:4.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8c8cb4a267669986cd76225679b8b78b\transformed\zxing-android-embedded-4.2.0\AndroidManifest.xml:25:5-27:36
49        android:name="android.hardware.camera"
49-->[com.journeyapps:zxing-android-embedded:4.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8c8cb4a267669986cd76225679b8b78b\transformed\zxing-android-embedded-4.2.0\AndroidManifest.xml:26:9-47
50        android:required="false" />
50-->[com.journeyapps:zxing-android-embedded:4.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8c8cb4a267669986cd76225679b8b78b\transformed\zxing-android-embedded-4.2.0\AndroidManifest.xml:27:9-33
51    <uses-feature
51-->[com.journeyapps:zxing-android-embedded:4.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8c8cb4a267669986cd76225679b8b78b\transformed\zxing-android-embedded-4.2.0\AndroidManifest.xml:28:5-30:36
52        android:name="android.hardware.camera.front"
52-->[com.journeyapps:zxing-android-embedded:4.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8c8cb4a267669986cd76225679b8b78b\transformed\zxing-android-embedded-4.2.0\AndroidManifest.xml:29:9-53
53        android:required="false" /> <!-- TODO replace above two with next line after Android 4.2 -->
53-->[com.journeyapps:zxing-android-embedded:4.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8c8cb4a267669986cd76225679b8b78b\transformed\zxing-android-embedded-4.2.0\AndroidManifest.xml:30:9-33
54    <!-- <uses-feature android:name="android.hardware.camera.any"/> -->
55    <uses-feature
55-->[com.journeyapps:zxing-android-embedded:4.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8c8cb4a267669986cd76225679b8b78b\transformed\zxing-android-embedded-4.2.0\AndroidManifest.xml:33:5-35:36
56        android:name="android.hardware.camera.autofocus"
56-->[com.journeyapps:zxing-android-embedded:4.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8c8cb4a267669986cd76225679b8b78b\transformed\zxing-android-embedded-4.2.0\AndroidManifest.xml:34:9-57
57        android:required="false" />
57-->[com.journeyapps:zxing-android-embedded:4.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8c8cb4a267669986cd76225679b8b78b\transformed\zxing-android-embedded-4.2.0\AndroidManifest.xml:35:9-33
58    <uses-feature
58-->[com.journeyapps:zxing-android-embedded:4.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8c8cb4a267669986cd76225679b8b78b\transformed\zxing-android-embedded-4.2.0\AndroidManifest.xml:36:5-38:36
59        android:name="android.hardware.camera.flash"
59-->[com.journeyapps:zxing-android-embedded:4.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8c8cb4a267669986cd76225679b8b78b\transformed\zxing-android-embedded-4.2.0\AndroidManifest.xml:37:9-53
60        android:required="false" />
60-->[com.journeyapps:zxing-android-embedded:4.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8c8cb4a267669986cd76225679b8b78b\transformed\zxing-android-embedded-4.2.0\AndroidManifest.xml:38:9-33
61    <uses-feature
61-->[com.journeyapps:zxing-android-embedded:4.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8c8cb4a267669986cd76225679b8b78b\transformed\zxing-android-embedded-4.2.0\AndroidManifest.xml:39:5-41:36
62        android:name="android.hardware.screen.landscape"
62-->[com.journeyapps:zxing-android-embedded:4.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8c8cb4a267669986cd76225679b8b78b\transformed\zxing-android-embedded-4.2.0\AndroidManifest.xml:40:9-57
63        android:required="false" />
63-->[com.journeyapps:zxing-android-embedded:4.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8c8cb4a267669986cd76225679b8b78b\transformed\zxing-android-embedded-4.2.0\AndroidManifest.xml:41:9-33
64    <uses-feature
64-->[com.journeyapps:zxing-android-embedded:4.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8c8cb4a267669986cd76225679b8b78b\transformed\zxing-android-embedded-4.2.0\AndroidManifest.xml:42:5-44:36
65        android:name="android.hardware.wifi"
65-->[com.journeyapps:zxing-android-embedded:4.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8c8cb4a267669986cd76225679b8b78b\transformed\zxing-android-embedded-4.2.0\AndroidManifest.xml:43:9-45
66        android:required="false" />
66-->[com.journeyapps:zxing-android-embedded:4.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8c8cb4a267669986cd76225679b8b78b\transformed\zxing-android-embedded-4.2.0\AndroidManifest.xml:44:9-33
67
68    <permission
68-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\48fbfb4201531ba0d2c54a69b6a94add\transformed\core-1.9.0\AndroidManifest.xml:22:5-24:47
69        android:name="com.example.aimusicplayer.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
69-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\48fbfb4201531ba0d2c54a69b6a94add\transformed\core-1.9.0\AndroidManifest.xml:23:9-81
70        android:protectionLevel="signature" />
70-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\48fbfb4201531ba0d2c54a69b6a94add\transformed\core-1.9.0\AndroidManifest.xml:24:9-44
71
72    <uses-permission android:name="com.example.aimusicplayer.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
72-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\48fbfb4201531ba0d2c54a69b6a94add\transformed\core-1.9.0\AndroidManifest.xml:26:5-97
72-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\48fbfb4201531ba0d2c54a69b6a94add\transformed\core-1.9.0\AndroidManifest.xml:26:22-94
73
74    <application
74-->C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:40:5-109:19
75        android:name="com.example.aimusicplayer.MusicApplication"
75-->C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:41:9-41
76        android:allowBackup="true"
76-->C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:42:9-35
77        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
77-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\48fbfb4201531ba0d2c54a69b6a94add\transformed\core-1.9.0\AndroidManifest.xml:28:18-86
78        android:dataExtractionRules="@xml/data_extraction_rules"
78-->C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:43:9-65
79        android:debuggable="true"
80        android:extractNativeLibs="false"
81        android:fullBackupContent="@xml/backup_rules"
81-->C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:44:9-54
82        android:icon="@mipmap/ic_launcher"
82-->C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:45:9-43
83        android:label="轻聆"
83-->C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:46:9-27
84        android:networkSecurityConfig="@xml/network_security_config"
84-->C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:51:9-69
85        android:requestLegacyExternalStorage="true"
85-->C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:49:9-52
86        android:supportsRtl="true"
86-->C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:47:9-35
87        android:testOnly="true"
88        android:theme="@style/Theme.AIMusicPlayer"
88-->C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:48:9-51
89        android:usesCleartextTraffic="true" >
89-->C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:50:9-44
90
91        <!-- 百度语音SDK必要的meta-data配置 -->
92        <meta-data
92-->C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:55:9-57:41
93            android:name="com.baidu.speech.APP_ID"
93-->C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:56:13-51
94            android:value="118558442" />
94-->C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:57:13-38
95        <meta-data
95-->C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:58:9-60:56
96            android:name="com.baidu.speech.API_KEY"
96-->C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:59:13-52
97            android:value="l07tTLiM8XdSVcM6Avmv5FG3" />
97-->C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:60:13-53
98        <meta-data
98-->C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:61:9-63:64
99            android:name="com.baidu.speech.SECRET_KEY"
99-->C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:62:13-55
100            android:value="e4DxN5gewACp162txczyVRuJs4UGBhdb" />
100-->C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:63:13-61
101
102        <!-- 启动页 -->
103        <activity
103-->C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:66:9-75:20
104            android:name="com.example.aimusicplayer.ui.splash.SplashActivity"
104-->C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:67:13-53
105            android:exported="true"
105-->C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:68:13-36
106            android:screenOrientation="landscape"
106-->C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:69:13-50
107            android:theme="@style/FullScreenTheme" >
107-->C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:70:13-51
108            <intent-filter>
108-->C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:71:13-74:29
109                <action android:name="android.intent.action.MAIN" />
109-->C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:72:17-69
109-->C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:72:25-66
110
111                <category android:name="android.intent.category.LAUNCHER" />
111-->C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:73:17-77
111-->C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:73:27-74
112            </intent-filter>
113        </activity>
114
115        <!-- 登录页 -->
116        <activity
116-->C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:78:9-82:54
117            android:name="com.example.aimusicplayer.ui.login.LoginActivity"
117-->C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:79:13-51
118            android:exported="false"
118-->C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:80:13-37
119            android:screenOrientation="landscape"
119-->C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:81:13-50
120            android:theme="@style/FullScreenTheme" />
120-->C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:82:13-51
121
122        <!-- 主界面（已重命名，移除了MainActivity2） -->
123        <activity
123-->C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:85:9-89:54
124            android:name="com.example.aimusicplayer.ui.main.MainActivity"
124-->C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:86:13-49
125            android:exported="false"
125-->C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:87:13-37
126            android:screenOrientation="landscape"
126-->C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:88:13-50
127            android:theme="@style/FullScreenTheme" />
127-->C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:89:13-51
128
129        <!-- 播放服务 -->
130        <service
130-->C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:92:9-95:40
131            android:name="com.example.aimusicplayer.service.UnifiedPlaybackService"
131-->C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:93:13-59
132            android:enabled="true"
132-->C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:94:13-35
133            android:exported="false" />
133-->C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:95:13-37
134
135        <!-- 播放器页面已迁移到Fragment，不再需要单独的Activity -->
136        <!-- PlayerActivity已删除，功能已迁移到PlayerFragment -->
137
138        <provider
139            android:name="androidx.core.content.FileProvider"
139-->C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:101:13-62
140            android:authorities="com.example.aimusicplayer.provider"
140-->C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:102:13-60
141            android:exported="false"
141-->C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:103:13-37
142            android:grantUriPermissions="true" >
142-->C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:104:13-47
143            <meta-data
143-->C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:105:13-107:54
144                android:name="android.support.FILE_PROVIDER_PATHS"
144-->C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:106:17-67
145                android:resource="@xml/file_paths" />
145-->C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:107:17-51
146        </provider>
147
148        <activity
148-->[com.karumi:dexter:6.2.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\97b63a40a644c4ff2b899324bd1d1a02\transformed\dexter-6.2.3\AndroidManifest.xml:27:9-29:72
149            android:name="com.karumi.dexter.DexterActivity"
149-->[com.karumi:dexter:6.2.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\97b63a40a644c4ff2b899324bd1d1a02\transformed\dexter-6.2.3\AndroidManifest.xml:28:13-60
150            android:theme="@style/Dexter.Internal.Theme.Transparent" />
150-->[com.karumi:dexter:6.2.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\97b63a40a644c4ff2b899324bd1d1a02\transformed\dexter-6.2.3\AndroidManifest.xml:29:13-69
151        <activity
151-->[com.journeyapps:zxing-android-embedded:4.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8c8cb4a267669986cd76225679b8b78b\transformed\zxing-android-embedded-4.2.0\AndroidManifest.xml:47:9-53:63
152            android:name="com.journeyapps.barcodescanner.CaptureActivity"
152-->[com.journeyapps:zxing-android-embedded:4.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8c8cb4a267669986cd76225679b8b78b\transformed\zxing-android-embedded-4.2.0\AndroidManifest.xml:48:13-74
153            android:clearTaskOnLaunch="true"
153-->[com.journeyapps:zxing-android-embedded:4.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8c8cb4a267669986cd76225679b8b78b\transformed\zxing-android-embedded-4.2.0\AndroidManifest.xml:49:13-45
154            android:screenOrientation="sensorLandscape"
154-->[com.journeyapps:zxing-android-embedded:4.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8c8cb4a267669986cd76225679b8b78b\transformed\zxing-android-embedded-4.2.0\AndroidManifest.xml:50:13-56
155            android:stateNotNeeded="true"
155-->[com.journeyapps:zxing-android-embedded:4.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8c8cb4a267669986cd76225679b8b78b\transformed\zxing-android-embedded-4.2.0\AndroidManifest.xml:51:13-42
156            android:theme="@style/zxing_CaptureTheme"
156-->[com.journeyapps:zxing-android-embedded:4.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8c8cb4a267669986cd76225679b8b78b\transformed\zxing-android-embedded-4.2.0\AndroidManifest.xml:52:13-54
157            android:windowSoftInputMode="stateAlwaysHidden" />
157-->[com.journeyapps:zxing-android-embedded:4.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8c8cb4a267669986cd76225679b8b78b\transformed\zxing-android-embedded-4.2.0\AndroidManifest.xml:53:13-60
158
159        <meta-data
159-->[com.github.bumptech.glide:okhttp3-integration:4.16.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b2253a27b5dc759780db3c74df291e5b\transformed\okhttp3-integration-4.16.0\AndroidManifest.xml:10:9-12:43
160            android:name="com.bumptech.glide.integration.okhttp3.OkHttpGlideModule"
160-->[com.github.bumptech.glide:okhttp3-integration:4.16.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b2253a27b5dc759780db3c74df291e5b\transformed\okhttp3-integration-4.16.0\AndroidManifest.xml:11:13-84
161            android:value="GlideModule" />
161-->[com.github.bumptech.glide:okhttp3-integration:4.16.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b2253a27b5dc759780db3c74df291e5b\transformed\okhttp3-integration-4.16.0\AndroidManifest.xml:12:13-40
162
163        <provider
163-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9ee23484e15125fd8d0a8afb0f0603a9\transformed\emoji2-1.2.0\AndroidManifest.xml:24:9-32:20
164            android:name="androidx.startup.InitializationProvider"
164-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9ee23484e15125fd8d0a8afb0f0603a9\transformed\emoji2-1.2.0\AndroidManifest.xml:25:13-67
165            android:authorities="com.example.aimusicplayer.androidx-startup"
165-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9ee23484e15125fd8d0a8afb0f0603a9\transformed\emoji2-1.2.0\AndroidManifest.xml:26:13-68
166            android:exported="false" >
166-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9ee23484e15125fd8d0a8afb0f0603a9\transformed\emoji2-1.2.0\AndroidManifest.xml:27:13-37
167            <meta-data
167-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9ee23484e15125fd8d0a8afb0f0603a9\transformed\emoji2-1.2.0\AndroidManifest.xml:29:13-31:52
168                android:name="androidx.emoji2.text.EmojiCompatInitializer"
168-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9ee23484e15125fd8d0a8afb0f0603a9\transformed\emoji2-1.2.0\AndroidManifest.xml:30:17-75
169                android:value="androidx.startup" />
169-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9ee23484e15125fd8d0a8afb0f0603a9\transformed\emoji2-1.2.0\AndroidManifest.xml:31:17-49
170            <meta-data
170-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3561efb20ac5d6136e1c3393e8a6bbc0\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:29:13-31:52
171                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
171-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3561efb20ac5d6136e1c3393e8a6bbc0\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:30:17-78
172                android:value="androidx.startup" />
172-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3561efb20ac5d6136e1c3393e8a6bbc0\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:31:17-49
173            <meta-data
173-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ae706378acc0de26a672fefdc1892496\transformed\profileinstaller-1.3.0\AndroidManifest.xml:29:13-31:52
174                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
174-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ae706378acc0de26a672fefdc1892496\transformed\profileinstaller-1.3.0\AndroidManifest.xml:30:17-85
175                android:value="androidx.startup" />
175-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ae706378acc0de26a672fefdc1892496\transformed\profileinstaller-1.3.0\AndroidManifest.xml:31:17-49
176        </provider>
177
178        <uses-library
178-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\42b358dfc31cecf53833e2b1f5ca9c9c\transformed\window-1.0.0\AndroidManifest.xml:25:9-27:40
179            android:name="androidx.window.extensions"
179-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\42b358dfc31cecf53833e2b1f5ca9c9c\transformed\window-1.0.0\AndroidManifest.xml:26:13-54
180            android:required="false" />
180-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\42b358dfc31cecf53833e2b1f5ca9c9c\transformed\window-1.0.0\AndroidManifest.xml:27:13-37
181        <uses-library
181-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\42b358dfc31cecf53833e2b1f5ca9c9c\transformed\window-1.0.0\AndroidManifest.xml:28:9-30:40
182            android:name="androidx.window.sidecar"
182-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\42b358dfc31cecf53833e2b1f5ca9c9c\transformed\window-1.0.0\AndroidManifest.xml:29:13-51
183            android:required="false" />
183-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\42b358dfc31cecf53833e2b1f5ca9c9c\transformed\window-1.0.0\AndroidManifest.xml:30:13-37
184
185        <service
185-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\34d010526148f493071b1b4f5da875cc\transformed\room-runtime-2.6.1\AndroidManifest.xml:24:9-28:63
186            android:name="androidx.room.MultiInstanceInvalidationService"
186-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\34d010526148f493071b1b4f5da875cc\transformed\room-runtime-2.6.1\AndroidManifest.xml:25:13-74
187            android:directBootAware="true"
187-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\34d010526148f493071b1b4f5da875cc\transformed\room-runtime-2.6.1\AndroidManifest.xml:26:13-43
188            android:exported="false" />
188-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\34d010526148f493071b1b4f5da875cc\transformed\room-runtime-2.6.1\AndroidManifest.xml:27:13-37
189
190        <receiver
190-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ae706378acc0de26a672fefdc1892496\transformed\profileinstaller-1.3.0\AndroidManifest.xml:34:9-52:20
191            android:name="androidx.profileinstaller.ProfileInstallReceiver"
191-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ae706378acc0de26a672fefdc1892496\transformed\profileinstaller-1.3.0\AndroidManifest.xml:35:13-76
192            android:directBootAware="false"
192-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ae706378acc0de26a672fefdc1892496\transformed\profileinstaller-1.3.0\AndroidManifest.xml:36:13-44
193            android:enabled="true"
193-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ae706378acc0de26a672fefdc1892496\transformed\profileinstaller-1.3.0\AndroidManifest.xml:37:13-35
194            android:exported="true"
194-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ae706378acc0de26a672fefdc1892496\transformed\profileinstaller-1.3.0\AndroidManifest.xml:38:13-36
195            android:permission="android.permission.DUMP" >
195-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ae706378acc0de26a672fefdc1892496\transformed\profileinstaller-1.3.0\AndroidManifest.xml:39:13-57
196            <intent-filter>
196-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ae706378acc0de26a672fefdc1892496\transformed\profileinstaller-1.3.0\AndroidManifest.xml:40:13-42:29
197                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
197-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ae706378acc0de26a672fefdc1892496\transformed\profileinstaller-1.3.0\AndroidManifest.xml:41:17-91
197-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ae706378acc0de26a672fefdc1892496\transformed\profileinstaller-1.3.0\AndroidManifest.xml:41:25-88
198            </intent-filter>
199            <intent-filter>
199-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ae706378acc0de26a672fefdc1892496\transformed\profileinstaller-1.3.0\AndroidManifest.xml:43:13-45:29
200                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
200-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ae706378acc0de26a672fefdc1892496\transformed\profileinstaller-1.3.0\AndroidManifest.xml:44:17-85
200-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ae706378acc0de26a672fefdc1892496\transformed\profileinstaller-1.3.0\AndroidManifest.xml:44:25-82
201            </intent-filter>
202            <intent-filter>
202-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ae706378acc0de26a672fefdc1892496\transformed\profileinstaller-1.3.0\AndroidManifest.xml:46:13-48:29
203                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
203-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ae706378acc0de26a672fefdc1892496\transformed\profileinstaller-1.3.0\AndroidManifest.xml:47:17-88
203-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ae706378acc0de26a672fefdc1892496\transformed\profileinstaller-1.3.0\AndroidManifest.xml:47:25-85
204            </intent-filter>
205            <intent-filter>
205-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ae706378acc0de26a672fefdc1892496\transformed\profileinstaller-1.3.0\AndroidManifest.xml:49:13-51:29
206                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
206-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ae706378acc0de26a672fefdc1892496\transformed\profileinstaller-1.3.0\AndroidManifest.xml:50:17-95
206-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ae706378acc0de26a672fefdc1892496\transformed\profileinstaller-1.3.0\AndroidManifest.xml:50:25-92
207            </intent-filter>
208        </receiver>
209    </application>
210
211</manifest>
